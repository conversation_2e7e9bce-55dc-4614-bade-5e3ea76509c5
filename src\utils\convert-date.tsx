export function convertDate(date: Date) {
  return new Intl.DateTimeFormat('pt-BR', {
    year: 'numeric',
    month: 'long',
  })
    .format(new Date(date))
    .replace(' de', '')
}

export function convertDateToCalendar(date: Date) {
  return new Intl.DateTimeFormat('pt-BR', {
    year: 'numeric',
    month: 'numeric',
    day: 'numeric',
  }).format(new Date(date))
}

export function getStartOfDay(date: Date, options?: { utc: boolean }) {
  const useUTC = options?.utc !== false

  if (useUTC) {
    return new Date(
      Date.UTC(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate())
    )
  }

  const localDate = new Date(date)
  localDate.setHours(0, 0, 0)
  return localDate
}

export function getEndOfDay(date: Date, options?: { utc: boolean }) {
  const useUTC = options?.utc !== false

  if (useUTC) {
    return new Date(
      Date.UTC(
        date.getUTCFullYear(),
        date.getUTCMonth(),
        date.getUTCDate(),
        23,
        59,
        59,
        999
      )
    )
  }

  const localDate = new Date(date)
  localDate.setHours(23, 59, 59, 999)
  return localDate
}

export function subtractDays(date: Date, days: number): Date {
  const newDate = new Date(date)
  newDate.setDate(newDate.getDate() - days)
  return newDate
}

export function subtractYears(date: Date, years: number): Date {
  const newDate = new Date(date)
  newDate.setFullYear(newDate.getFullYear() - years)
  return newDate
}
