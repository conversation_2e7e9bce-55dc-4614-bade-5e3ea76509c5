import { create } from 'zustand'

type CourseSelected = {
  id: number
  title: string
}

type CoursesStore = {
  coursesSelected: CourseSelected[]
  toggleCourse: (course: CourseSelected) => void
  isCursoSelecionado: (id: number) => boolean
  setCoursesSelected: (courses: CourseSelected[]) => void
}

export const useSelectedCoursesTrail = create<CoursesStore>((set, get) => ({
  coursesSelected: [],

  toggleCourse: (course) => {
    const { coursesSelected } = get()
    const exists = coursesSelected.some((c) => c.id === course.id)

    if (exists) {
      set({
        coursesSelected: coursesSelected.filter((c) => c.id !== course.id),
      })
    } else {
      set({
        coursesSelected: [...coursesSelected, course],
      })
    }
  },

  isCursoSelecionado: (id) => get().coursesSelected.some((c) => c.id === id),

  setCoursesSelected: (courses) => set({ coursesSelected: courses }),
}))
