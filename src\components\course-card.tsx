'use client'

import { Checkbox, Tooltip } from '@ads/components-react'
import Image from 'next/image'
import { useEffect, useRef, useState } from 'react'

import { cn } from '@/lib/utils'

interface CardCourseProps {
  id: string
  index: number
  imageUrl: string
  imageAlt: string
  title: string
  justification: string
  checked?: boolean
  messageId?: string
  onChange?: (id: string, checked: boolean, messageId?: string) => void
}

export function CourseCard({
  id,
  imageUrl,
  imageAlt,
  title,
  justification,
  checked,
  messageId,
  onChange,
}: CardCourseProps) {
  const [isOverflowing, setIsOverflowing] = useState(false)

  const textRef = useRef<HTMLSpanElement>(null)

  function handleCheckboxChange() {
    if (onChange) {
      onChange(id, !checked, messageId)
    }
  }

  useEffect(() => {
    if (textRef.current) {
      const { scrollHeight, clientHeight } = textRef.current
      setIsOverflowing(scrollHeight > clientHeight)
    }
  }, [justification])

  return (
    <article
      role="button"
      tabIndex={0}
      onClick={handleCheckboxChange}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault()
          handleCheckboxChange()
        }
      }}
      className={cn(
        'flex h-full w-full cursor-pointer flex-col justify-between overflow-hidden rounded-xl border border-solid bg-ctx-interactive-secondary px-4 py-3 transition-all duration-200 hover:bg-ctx-interactive-secondaryHover',
        checked
          ? 'border-ctx-highlight-focus'
          : 'border-ctx-interactive-secondary'
      )}
      aria-checked={checked}
    >
      <section className="flex h-full flex-col">
        <header className="flex flex-grow gap-4">
          <figure className="flex h-[142px] w-full max-w-[97px] flex-shrink-0 items-center justify-center rounded-lg border border-solid border-ctx-interactive-secondary bg-ctx-content-placeholderAlternative">
            <div className="relative h-[142px] w-[97px]">
              <Image
                src={imageUrl}
                alt={imageAlt}
                fill
                className="rounded-md object-cover"
              />
            </div>
          </figure>

          <div className="flex flex-grow flex-col space-y-2 py-4">
            <h3 className="line-clamp-2 text-ellipsis text-ctx-content-title ts-subtitle-xxs">
              {title}
            </h3>
            <span
              ref={textRef}
              className="line-clamp-4 text-ellipsis text-ctx-content-base ts-paragraph-xxxs md:line-clamp-3"
            >
              Justificativa: {justification}
            </span>

            {isOverflowing && (
              <div className="hidden justify-end md:flex">
                <Tooltip title={justification} side="bottom">
                  <span className="text-ctx-content-title underline ts-paragraph-xxxs">
                    Ver mais
                  </span>
                </Tooltip>
              </div>
            )}
          </div>
        </header>

        {onChange && (
          <footer className="mt-auto flex items-center justify-end space-x-2 pt-2">
            <Checkbox
              checked={checked}
              onChange={() => onChange(id, !checked)}
            />

            <p className="text-ctx-content-title ts-paragraph-xxs">
              {!checked ? 'Selecionar curso' : 'Curso selecionado'}
            </p>
          </footer>
        )}
      </section>
    </article>
  )
}
