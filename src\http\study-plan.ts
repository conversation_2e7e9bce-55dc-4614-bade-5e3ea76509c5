import {
  CreateStudyPlanMutation,
  CreateStudyPlanMutationVariables,
  GetStudyPlansQuery,
  GetStudyPlansQueryVariables,
  type UpdatedTeamsAndUserPlanStudyMutation,
  type UpdatedTeamsAndUserPlanStudyMutationVariables,
} from '@/graphql/generated/graphql'
import {
  CREATE_STUDY_PLAN,
  DELETE_STUDY_PLAN,
  UPDATE_TEAMS_AND_USER_STUDY_PLAN,
} from '@/graphql/study-plan/mutations'
import { GET_STUDY_PLANS } from '@/graphql/study-plan/queries'
import { clientGraphql } from '@/services/graphql'

export const getStudyPlans = (data: GetStudyPlansQueryVariables) =>
  clientGraphql.request<GetStudyPlansQuery>(
    GET_STUDY_PLANS,
    {
      ...data,
    },
    {
      'cache-control': 'no-cache',
    }
  )

export const createNewStudyPlan = (data: CreateStudyPlanMutationVariables) =>
  clientGraphql.request<CreateStudyPlanMutation>(CREATE_STUDY_PLAN, {
    ...data,
  })

export const deleteStudyPlan = (id: number) =>
  clientGraphql.request(DELETE_STUDY_PLAN, {
    id,
  })

export const updateTeamsUsersStudyPlan = (
  data: UpdatedTeamsAndUserPlanStudyMutationVariables
) =>
  clientGraphql.request<UpdatedTeamsAndUserPlanStudyMutation>(
    UPDATE_TEAMS_AND_USER_STUDY_PLAN,
    {
      ...data,
    }
  )
