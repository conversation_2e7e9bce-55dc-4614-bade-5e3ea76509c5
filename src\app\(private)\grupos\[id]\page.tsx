'use client'

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, useAlert } from '@ads/components-react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { useEffect, useMemo, useState } from 'react'
import { useMutation, useQuery } from 'react-query'
import Cookies from 'universal-cookie'

import { CancelEditTeamAlert } from '@/components/alerts-dialogs/cancel-edit-team'
import { GroupUsersSelectedTable } from '@/components/tables/group/users-selected'
import { AddUsersModal } from '@/components/ui/groups/modal'
import { Skeleton } from '@/components/ui/skeleton'
import { getGroupById, updateGroup } from '@/http/groups'
import { getB2bUsers } from '@/http/user'
import { queryClient } from '@/lib/queryClient'
import { useSelectedUsersGroups } from '@/store/useSelectedUsersGroup'

import { EmptyList } from '../empty-list'

export default function EquipesId() {
  const router = useRouter()
  const params = useParams()

  const { alert } = useAlert()
  const {
    selectedUserIds,
    selectedAll,
    setTempSelected,
    setSelected,
    clearSelected,
  } = useSelectedUsersGroups()

  const [groupName, setGroupName] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [error, setError] = useState(false)

  const { companyId, enrollmentId } = (() => {
    const cookies = new Cookies(null, { path: '/' })

    return {
      companyId: cookies.get('b2bCompanyId'),
      enrollmentId: Number(cookies.get('b2bEnrollmentId')),
    }
  })()

  const groupId = params.id
  const perPage = 10

  const { mutate: onUpdateGroup, isLoading: isLoadingUpdate } = useMutation({
    mutationFn: updateGroup,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['getGroupsGql'],
        exact: false,
      })
      alert({
        title: 'Equipe atualiza com sucesso!',
        description: `A equipe ${groupName} foi atualizada`,
        alertType: 'success',
      })
      handleBackPage()
    },
    onError: () => {
      alert({
        title: 'Erro ao atualizar equipe!',
        description: `Não foi possível atualizar está equipe. Tente novamente.`,
        alertType: 'danger',
      })
    },
  })

  const { data: group, isLoading: isLoadingGroup } = useQuery({
    queryKey: ['getGroupByIdGql', groupId],
    staleTime: 0,
    cacheTime: 0,
    queryFn: () =>
      getGroupById({
        id: Number(groupId),
        page: 1,
        limit: 9999,
      }),
    onError: () => {
      alert({
        title: 'Grupo não encontrado!',
        description: `Não foi possível localizar este grupo. Tente novamente.`,
        alertType: 'danger',
      })
      handleBackPage()
    },
  })

  const { data: usersResponse, isLoading: isLoadingUsers } = useQuery({
    queryKey: ['getb2bUsersSelectedGql'],
    keepPreviousData: true,
    staleTime: 1000 * 60 * 10, // 10 minutes
    queryFn: () =>
      getB2bUsers({
        page: 1,
        limit: 9999,
        orderBy: 'NAME',
        company_id: companyId,
        enrollment_id: enrollmentId,
      }),
    onError: () => {
      alert({
        title: 'Colaboradores não encontrados!',
        description: `Não foi possível localizar os colaboradores desta equipe. Tente novamente.`,
        alertType: 'danger',
      })
      handleBackPage()
    },
  })

  const allUsers = usersResponse?.users.data ?? []
  const groupUsers = group?.companyGroup?.groupUsers?.data ?? []

  const allSelectableUsers = useMemo(() => {
    const uniqueUsersMap = new Map()

    allUsers.forEach((user) => {
      uniqueUsersMap.set(user.id, user)
    })

    groupUsers.forEach((user) => {
      if (!uniqueUsersMap.has(user.id)) {
        uniqueUsersMap.set(user.id, user)
      }
    })

    return Array.from(uniqueUsersMap.values())
  }, [allUsers, groupUsers])

  const filteredUsers = useMemo(
    () =>
      allSelectableUsers.filter((user) =>
        selectedAll
          ? !selectedUserIds.includes(user.id)
          : selectedUserIds.includes(user.id)
      ),
    [allSelectableUsers, selectedAll, selectedUserIds]
  )

  const paginatedUsers = useMemo(
    () =>
      filteredUsers.slice((currentPage - 1) * perPage, currentPage * perPage),
    [filteredUsers, currentPage, perPage]
  )

  const usersByCompanySelected = useMemo(
    () => ({
      data: paginatedUsers,
      total: filteredUsers.length,
      perPage,
    }),
    [paginatedUsers, filteredUsers.length, perPage]
  )

  const isTeamDataUnchanged =
    selectedUserIds.length === groupUsers.length &&
    groupUsers.every((user) => selectedUserIds.includes(user.id)) &&
    group?.companyGroup.name === groupName

  const shouldReturnEmptyList =
    !isLoadingUsers &&
    !isLoadingGroup &&
    ((!selectedAll && !selectedUserIds.length) ||
      (selectedAll && selectedUserIds.length === allUsers.length))

  const handleUpdateGroup = () => {
    if (!groupName) {
      alert({
        alertType: 'danger',
        title: 'Grupo sem nome',
        description: 'Por favor, informe o nome do grupo.',
      })
      setError(true)
      return
    }

    if (
      (!selectedAll && !selectedUserIds.length) ||
      (selectedAll && selectedUserIds.length === allUsers.length)
    ) {
      alert({
        alertType: 'danger',
        title: 'Grupo sem colaboradores',
        description: 'Selecione ao menos 1 colaborador.',
      })
      return
    }

    const user_ids_deselected = selectedAll
      ? selectedUserIds.length
        ? selectedUserIds
        : null
      : (() => {
          const deselected = groupUsers
            .filter((user) => !selectedUserIds.includes(user.id))
            .map((user) => user.id)

          return deselected.length ? deselected : null
        })()

    onUpdateGroup({
      id: Number(groupId),
      name: groupName,
      select_all_user_ids: selectedAll,
      user_ids_deselected,
      user_ids_selected: !selectedAll ? selectedUserIds : null,
    })
  }

  const handleBackPage = () => {
    clearSelected()
    router.push('/grupos')
  }

  useEffect(() => {
    if (!group) return

    clearSelected()

    setGroupName(group.companyGroup?.name)

    const userIds =
      group.companyGroup?.groupUsers?.data.map(({ id }) => id) || []
    setSelected(userIds)
    setTempSelected(userIds)
  }, [group?.companyGroup, groupId])

  return (
    <>
      <div className="rounded-2xl bg-ctx-layout-body p-4 md:p-8">
        <div className="flex flex-col items-end justify-between gap-8 rounded-2xl border border-solid border-ctx-layout-border p-4 md:flex-row">
          {isLoadingGroup ? (
            <Skeleton className="mt-6 h-10 w-full md:w-4/5" />
          ) : (
            <>
              <TextField
                label="Nome"
                value={groupName}
                hasError={error}
                onChange={(e) => setGroupName(e.target.value)}
                size="md"
                placeholder="Digite o nome da Equipe"
                fullWidth
                custom={{
                  input: 'bg-ctx-interactive-secondary',
                }}
              />

              <AddUsersModal />
            </>
          )}
        </div>

        <div className="mt-6 w-full">
          {shouldReturnEmptyList ? (
            <EmptyList
              title="Sem Colaborador"
              description="Nenhum colaborador foi atribuido."
            />
          ) : (
            <GroupUsersSelectedTable
              isLoading={isLoadingGroup || isLoadingUsers}
              currentPage={currentPage}
              setCurrentPage={setCurrentPage}
              usersByCompany={usersByCompanySelected}
            />
          )}
        </div>
      </div>
      <div className="flex justify-end gap-8 py-6">
        <CancelEditTeamAlert
          onClickCancel={handleBackPage}
          disabledCancel={isLoadingUpdate}
          showAlert={!isTeamDataUnchanged}
        />
        <Button
          isLoading={isLoadingUpdate}
          disabled={isLoadingUpdate || isTeamDataUnchanged}
          onClick={handleUpdateGroup}
          size="md"
        >
          Salvar
        </Button>
      </div>
    </>
  )
}
