import { EPosition } from '@/enum/position'
import { ESeniority } from '@/enum/seniority'

export const allPositions: { value: EPosition; label: string }[] = [
  { value: EPosition.APPRENTICE, label: 'Aprendiz' },
  { value: EPosition.INTERN, label: '<PERSON>stagiá<PERSON>' },
  { value: EPosition.TRAINEE, label: 'Treinee' },
  { value: EPosition.ASSISTANT, label: 'Assistente' },
  { value: EPosition.AIDE, label: 'Auxiliar' },
  { value: EPosition.ATTENDANT, label: 'Atendente' },
  { value: EPosition.AGENT, label: 'Agente' },
  { value: EPosition.OPERATOR, label: 'Operador' },
  { value: EPosition.MONITOR, label: 'Monitor' },
  { value: EPosition.TECHNICIAN, label: 'Técnico' },
  { value: EPosition.ANALYST, label: 'Analista' },
  { value: EPosition.INSTRUCTOR, label: 'Instrutor' },
  { value: EPosition.SPECIALIST, label: 'Especialista' },
  { value: EPosition.CONSULTANT, label: 'Consultor' },
  { value: EPosition.SUPERVISOR, label: 'Supervisor' },
  { value: EPosition.COORDINATOR, label: 'Coordenador' },
  { value: EPosition.MANAGER, label: 'Gerente' },
  { value: EPosition.DIRECTOR, label: 'Diretor' },
]

export const allSeniorities: { value: ESeniority; label: string }[] = [
  { value: ESeniority.JUNIOR, label: 'Junior' },
  { value: ESeniority.MID_LEVEL, label: 'Pleno' },
  { value: ESeniority.SENIOR, label: 'Sênior' },
]

export const collaboratorTypes = [
  {
    id: '1',
    label: 'Aluno',
    name: 'aluno',
    value: 'student',
  },
  {
    id: '2',
    label: 'Gestor',
    name: 'admin',
    value: 'admin',
  },
]
