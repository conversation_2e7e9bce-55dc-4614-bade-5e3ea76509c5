import { Skeleton } from '@/components/ui/skeleton'

export function LicensesStatusSkeleton() {
  return (
    <div className="flex items-center justify-between gap-4 rounded-xl bg-ctx-layout-body px-6 py-2">
      <div className="flex-1 space-y-2">
        <Skeleton className="h-5 w-40 bg-ctx-layout-surface" />
        <div className="flex items-center gap-2">
          <Skeleton className="h-4 w-full bg-ctx-layout-surface md:max-w-sm" />{' '}
          <Skeleton className="h-4 w-8 bg-ctx-layout-surface" />{' '}
        </div>
      </div>

      <div className="hidden items-center gap-6 sm:flex">
        <Skeleton className="h-6 w-36 bg-ctx-layout-surface" />{' '}
        <Skeleton className="h-6 w-36 bg-ctx-layout-surface" />{' '}
      </div>
    </div>
  )
}
