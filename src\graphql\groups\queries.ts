import { gql } from 'graphql-request'

export const GET_B2B_USERS = gql`
  query GetB2BUsers(
    $all: Boolean!
    $page: Float!
    $limit: Float!
    $orderBy: EListOrderBy!
    $order: EListOrder!
    $company_id: Float!
    $enrollment_id: Float!
    $q: String
    $hasSquad: Boolean
  ) {
    users(
      all: $all
      page: $page
      limit: $limit
      orderBy: $orderBy
      order: $order
      company_id: $company_id
      enrollment_id: $enrollment_id
      q: $q
      hasSquad: $hasSquad
    ) {
      total
      perPage
      data {
        id
        name
        email
        last_login
        enrollments_pivot {
          status
        }
        roles {
          id
          name
          slug
        }
        metadata {
          last_activity_completed_at
          activities_completed
          squad_id
          company_squad {
            id
            title
          }
        }
      }
    }
  }
`

export const GET_GROUPS = gql`
  query GetGroups(
    $limit: Float
    $page: Float
    $company_id: Float!
    $name: String
  ) {
    companyGroups(
      limit: $limit
      page: $page
      company_id: $company_id
      name: $name
    ) {
      data {
        id
        name
        users_count
        updated_at
        company_id
        company_group_users_pivot {
          group_id
          user_id
        }
      }
      total
      perPage
    }
  }
`

export const GET_GROUP = gql`
  query GetGroup($id: Int!, $page: Int, $limit: Int) {
    companyGroup(id: $id) {
      id
      name
      groupUsers(limit: $limit, page: $page) {
        total
        perPage
        data {
          id
          name
          email
          updated_at
        }
      }
    }
  }
`
