import {
  CreateCompanyGroupMutationVariables,
  GetGroupQuery,
  GetGroupQueryVariables,
  GetGroupsQuery,
  GetGroupsQueryVariables,
  UpdateCompanyGroupInput,
} from '@/graphql/generated/graphql'
import {
  CREATE_NEW_GROUP,
  DELETE_GROUP,
  UPDATE_GROUP,
} from '@/graphql/groups/mutations'
import { GET_GROUP, GET_GROUPS } from '@/graphql/groups/queries'
import { clientGraphql } from '@/services/graphql'

export const createNewGroup = (data: CreateCompanyGroupMutationVariables) =>
  clientGraphql.request(CREATE_NEW_GROUP, {
    ...data,
  })

export const updateGroup = (data: UpdateCompanyGroupInput) =>
  clientGraphql.request(UPDATE_GROUP, {
    ...data,
  })

export const getGroups = (data: GetGroupsQueryVariables) =>
  clientGraphql.request<GetGroupsQuery>(
    GET_GROUPS,
    { ...data },
    { 'cache-control': 'no-cache' }
  )

export const getGroupById = (data: GetGroupQueryVariables) =>
  clientGraphql.request<GetGroupQuery>(
    GET_GROUP,
    { ...data },
    { 'cache-control': 'no-cache' }
  )

export const deleteGroup = (group_id: number) =>
  clientGraphql.request(DELETE_GROUP, { group_id })
