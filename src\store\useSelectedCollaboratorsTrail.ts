import { create } from 'zustand'

type CollaboratorSelected = {
  id: number
  name: string
}

type CollaboratorsStore = {
  collaboratorsSelected: CollaboratorSelected[]
  allCollaborators: boolean
  excludedCollaborators: number[]
  toggleCollaborator: (collaborator: CollaboratorSelected) => void
  isCollaboratorSelecionado: (id: number) => boolean
  setCollaboratorsSelected: (collaborators: CollaboratorSelected[]) => void
  checkAllCollaborators: () => void
}

export const useSelectedCollaboratorsTrail = create<CollaboratorsStore>(
  (set, get) => ({
    collaboratorsSelected: [],
    allCollaborators: false,
    excludedCollaborators: [],

    toggleCollaborator: (collaborator) => {
      const { allCollaborators, excludedCollaborators, collaboratorsSelected } =
        get()

      if (allCollaborators) {
        if (excludedCollaborators.includes(collaborator.id)) {
          set({
            excludedCollaborators: excludedCollaborators.filter(
              (id) => id !== collaborator.id
            ),
          })
        } else {
          set({
            excludedCollaborators: [...excludedCollaborators, collaborator.id],
          })
        }
      } else {
        const exists = collaboratorsSelected.some(
          (c) => c.id === collaborator.id
        )
        if (exists) {
          set({
            collaboratorsSelected: collaboratorsSelected.filter(
              (c) => c.id !== collaborator.id
            ),
          })
        } else {
          set({
            collaboratorsSelected: [...collaboratorsSelected, collaborator],
          })
        }
      }
    },

    isCollaboratorSelecionado: (id) => {
      const { allCollaborators, excludedCollaborators, collaboratorsSelected } =
        get()

      if (allCollaborators) {
        return !excludedCollaborators.includes(id)
      }

      return collaboratorsSelected.some((c) => c.id === id)
    },

    setCollaboratorsSelected: (collaborators) =>
      set({ collaboratorsSelected: collaborators }),

    checkAllCollaborators: () =>
      set((state) => ({
        allCollaborators: !state.allCollaborators,
        excludedCollaborators: [],
      })),
  })
)
