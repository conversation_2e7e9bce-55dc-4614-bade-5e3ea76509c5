'use client'

import { Button, useAlert } from '@ads/components-react'
import { useState } from 'react'
import { useMutation } from 'react-query'

import { PageLayout } from '@/components/layouts/page-layout'
import { timePeriodFilters, TimePeriodFilterTypes } from '@/consts/dashboard'
import { useAuth } from '@/contexts/AuthContext'
import { exportReport } from '@/http/dashboard/export-report'

import { CoursesRank } from './courses-rank'
import { TimeRangeFilters } from './time-range-filters'

export default function Dashboard() {
  const { user } = useAuth()
  const { alert } = useAlert()

  const [timePeriod, setTimePeriod] = useState<TimePeriodFilterTypes>(
    timePeriodFilters[2]
  )

  const { mutate: onExportReport, isLoading: isLoadingExportReport } =
    useMutation({
      mutationFn: exportReport,
      onSuccess: () => {
        alert({
          alertType: 'success',
          title: 'Relatório solicitado com sucesso',
          description:
            'O relatório está sendo gerado e dentro de alguns minutos estará disponível no seu e-mail.',
        })
      },
      onError: () => {
        alert({
          alertType: 'danger',
          title: 'Erro ao gerar o relatório.',
          description:
            'Ocorreu um erro ao gerar o relatório. Tente novamente mais tarde.',
        })
      },
    })

  const handleDownloadReport = () => {
    if (!timePeriod.values.end_date || !timePeriod.values.start_date) {
      return alert({
        alertType: 'danger',
        title: 'Erro ao gerar o relatório.',
        description:
          'Informe as datas de início e término para exportar o relatório.',
      })
    }

    onExportReport({
      company_id: Number(user?.metadata.company_id),
      end_date: timePeriod.values.end_date?.toString(),
      start_date: timePeriod.values.start_date?.toString(),
    })
  }

  return (
    <PageLayout
      title="Dashboard"
      description="Acompanhe os principais indicadores de engajamento, progresso das trilhas e desempenho dos colaboradores em tempo real."
      actionButton={
        <Button
          size="md"
          className="w-full sm:w-fit"
          isLoading={isLoadingExportReport}
          disabled={isLoadingExportReport}
          onClick={handleDownloadReport}
        >
          Baixar relatório
        </Button>
      }
    >
      <TimeRangeFilters
        timePeriod={timePeriod}
        onChangeTimeRange={setTimePeriod}
      />

      <div className="rounded-2xl bg-ctx-layout-body p-6 sm:p-8">
        <h2 className="text-ctx-content-title ts-heading-md">
          Cursos mais acessados
        </h2>
        <p className="mt-1 text-ctx-content-base ts-heading-xxs">
          Analise os cursos mais acessados
        </p>

        <CoursesRank
          end_date={timePeriod.values.end_date}
          start_date={timePeriod.values.start_date}
        />
      </div>
    </PageLayout>
  )
}
