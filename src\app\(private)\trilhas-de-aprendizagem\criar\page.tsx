'use client'

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Stepper<PERSON><PERSON>,
  use<PERSON><PERSON><PERSON>,
  useStepper,
} from '@ads/components-react'
import { zodResolver } from '@hookform/resolvers/zod'
import { useRouter } from 'next/navigation'
import { FormProvider, useForm } from 'react-hook-form'
import { useMutation } from 'react-query'
import Cookies from 'universal-cookie'

import { createNewStudyPlanV2 } from '@/http/study-plan'
import { useSelectedCollaboratorsTrail } from '@/store/useSelectedCollaboratorsTrail'
import { useSelectedCoursesTrail } from '@/store/useSelectedCoursesTrail'
import { useSelectedGroupsTrail } from '@/store/useSelectedGroupsTrail'
import { useSelectedSquadsTrail } from '@/store/useSelectedSquadsTrail'
import { useStepOneSaved } from '@/store/useStepOneSaved'
import { cookieSettings } from '@/utils/cookie-settings'

import { CancelNewStudyPlanAlert } from './components/cancel-alert'
import { StudyPlanQueriesProvider } from './contexts/StudyPlanQueriesContext'
import { Step1, Step2, Step3, Step4 } from './index'
import { CombinedCheckoutSchema, CombinedCheckoutType } from './validations'

export default function CriarTrilha() {
  const { alert } = useAlert()
  const router = useRouter()
  const cookies = new Cookies(null, { path: '/' })

  const companyId = cookies.get('b2bCompanyId')

  const { activeStep, previousStep, nextStep } = useStepper({
    initialStep: 0,
    stepAmount: 4,
  })

  const step1 = cookies.get('step1')

  const { setData } = useStepOneSaved()
  const { coursesSelected } = useSelectedCoursesTrail()
  const { squadsSelected, allSquads, excludedSquads } = useSelectedSquadsTrail()
  const { allGroups, excludedGroups, groupsSelected } = useSelectedGroupsTrail()
  const { collaboratorsSelected, allCollaborators, excludedCollaborators } =
    useSelectedCollaboratorsTrail()

  const methods = useForm<CombinedCheckoutType>({
    resolver: zodResolver(CombinedCheckoutSchema),
    defaultValues: {
      name: step1?.name || '',
      description: step1?.description || '',
      isMandatory: step1?.isMandatory || false,
      duration: step1?.duration || '',
      startDate: step1?.startDate || '',
      endDate: step1?.endDate || '',
    },
  })

  const handleClearCookies = () => {
    cookies.remove('step1')
    cookies.remove('step2')
    cookies.remove('step3')
  }

  const { mutate: createStudyPlanMutation } = useMutation({
    mutationFn: (data: CombinedCheckoutType) =>
      createNewStudyPlanV2({
        name: data.name,
        description: data.description || '',
        courses: coursesSelected.map((course, index) => ({
          course_id: Number(course.id),
          order: index,
        })),
        company_id: companyId,
        end_date: data.duration,
        ai_generated: false,
        squad_ids_selected:
          squadsSelected.length === 0
            ? null
            : squadsSelected.map((squad) => Number(squad.id)),
        user_ids_selected:
          collaboratorsSelected.length === 0
            ? null
            : collaboratorsSelected.map((collaborator) =>
                Number(collaborator.id)
              ),
        group_ids_selected:
          groupsSelected.length === 0
            ? null
            : groupsSelected.map((group) => Number(group.id)),
        is_pdi: false,
        select_all_group_ids: allGroups,
        select_all_squad_ids: allSquads,
        select_all_user_ids: allCollaborators,
        group_ids_deselected:
          excludedGroups.length === 0 ? null : excludedGroups,
        squad_ids_deselected:
          excludedSquads.length === 0 ? null : excludedSquads,
        user_ids_deselected:
          excludedCollaborators.length === 0 ? null : excludedCollaborators,
      }),
    onSuccess: () => {
      alert({
        title: 'Trilha de aprendizado criada com sucesso!',
        description: 'A trilha foi criada com sucesso.',
        alertType: 'success',
      })
      handleClearCookies()
      router.push('/trilhas-de-aprendizagem')
    },
    onError: () => {
      alert({
        title: 'Erro ao criar trilha de aprendizado',
        description:
          'Ocorreu um erro ao criar a trilha de aprendizado. Tente novamente mais tarde.',
        alertType: 'danger',
      })
    },
  })

  const handleSubmit = (data: CombinedCheckoutType) => {
    if (activeStep === 0 && !Object.keys(methods.formState.errors).length) {
      setData({
        name: data.name,
        description: data.description || '',
        isMandatory: !!data.isMandatory,
        duration: data.duration || '',
        startDate:
          data.startDate && data.startDate !== ''
            ? new Date(data.startDate as Date)
            : new Date(),
        endDate:
          data.endDate && data.endDate !== ''
            ? new Date(data.endDate as Date)
            : new Date(),
      })
      cookies.set('step1', JSON.stringify(data), cookieSettings())
      nextStep()
      return
    }

    if (activeStep === 1) {
      if (coursesSelected.length === 0) {
        alert({
          title: 'Trilha sem cursos',
          description: 'Por favor, selecione ao menos 1 curso.',
          alertType: 'danger',
        })
        return
      }
      cookies.set('step2', coursesSelected, cookieSettings())
      nextStep()
      return
    }

    if (activeStep === 2) {
      cookies.set(
        'step3',
        {
          collaborators: {
            all: allCollaborators,
            excluded: excludedCollaborators,
            selected: collaboratorsSelected,
          },
          squads: {
            all: allSquads,
            excluded: excludedSquads,
            selected: squadsSelected,
          },
          groups: {
            all: allGroups,
            excluded: excludedGroups,
            selected: groupsSelected,
          },
        },
        cookieSettings()
      )
      nextStep()
      return
    }

    if (activeStep === 3) {
      if (coursesSelected.length === 0) {
        alert({
          title: 'Trilha sem cursos',
          description: 'Por favor, selecione ao menos 1 curso.',
          alertType: 'danger',
        })
        return
      }
      createStudyPlanMutation(data)
    }
  }

  return (
    <StudyPlanQueriesProvider>
      <FormProvider {...methods}>
        <form
          className="m-auto flex w-full flex-col gap-8 px-2 py-4 lg:p-4"
          onSubmit={methods.handleSubmit(handleSubmit)}
        >
          <div>
            <h1 className="text-ctx-content-title ts-heading-md">
              Trilha de aprendizagem
            </h1>
            <span className="text-ctx-content-base ts-paragraph-xs">
              Gerencie, acompanhe e analise o progresso de desenvolvimento dos
              colaboradores em tempo real.
            </span>
          </div>
          <div className="flex w-full justify-center">
            <Stepper activeStep={activeStep}>
              <StepperItem title="Informações" />
              <StepperItem title="Selecionar cursos" />
              <StepperItem title="Selecionar participantes" />
              <StepperItem title="Resumo" />
            </Stepper>
          </div>
          <div>
            <StepperContent activeStep={activeStep} index={0}>
              <Step1 />
            </StepperContent>
            <StepperContent activeStep={activeStep} index={1}>
              <Step2 />
            </StepperContent>
            <StepperContent activeStep={activeStep} index={2}>
              <Step3 />
            </StepperContent>
            <StepperContent activeStep={activeStep} index={3}>
              <Step4 />
            </StepperContent>
          </div>
          <div className="flex w-full justify-end gap-2">
            {activeStep === 0 ? (
              <CancelNewStudyPlanAlert />
            ) : (
              <Button type="button" hierarchy="tertiary" onClick={previousStep}>
                Voltar
              </Button>
            )}

            <Button type="submit">Próximo</Button>
          </div>
        </form>
      </FormProvider>
    </StudyPlanQueriesProvider>
  )
}
