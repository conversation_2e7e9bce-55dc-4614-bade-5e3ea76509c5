'use client'

import { useAlert } from '@ads/components-react'
import { useRouter } from 'next/navigation'
import { useMutation } from 'react-query'
import Cookies from 'universal-cookie'

import { createNewStudyPlanV2 } from '@/http/study-plan'
import { useSelectedCollaboratorsTrail } from '@/store/useSelectedCollaboratorsTrail'
import { useSelectedCoursesTrail } from '@/store/useSelectedCoursesTrail'
import { useSelectedGroupsTrail } from '@/store/useSelectedGroupsTrail'
import { useSelectedSquadsTrail } from '@/store/useSelectedSquadsTrail'

import { FormStudyPlan } from '../components/form'
import { getDatePlusDays } from '../components/form/utils'
import { FormStudyPlanType } from '../components/form/validations'
import { StudyPlanQueriesProvider } from './contexts/StudyPlanQueriesContext'

export default function CriarTrilha() {
  const { alert } = useAlert()
  const router = useRouter()
  const cookies = new Cookies(null, { path: '/' })

  const companyId = cookies.get('b2bCompanyId')

  const { coursesSelected } = useSelectedCoursesTrail()
  const { squadsSelected, allSquads, excludedSquads } = useSelectedSquadsTrail()
  const { allGroups, excludedGroups, groupsSelected } = useSelectedGroupsTrail()
  const { collaboratorsSelected, allCollaborators, excludedCollaborators } =
    useSelectedCollaboratorsTrail()

  const { mutate: createStudyPlanMutation } = useMutation({
    mutationFn: (data: FormStudyPlanType) =>
      createNewStudyPlanV2({
        name: data.name,
        description: data.description || '',
        courses: coursesSelected.map((course, index) => ({
          course_id: Number(course.id),
          order: index,
        })),
        company_id: companyId,
        end_date:
          data.duration === '4'
            ? data.endDate
            : getDatePlusDays(Number(data.duration)),
        ai_generated: false,
        squad_ids_selected:
          squadsSelected.length === 0
            ? null
            : squadsSelected.map((squad) => Number(squad.id)),
        user_ids_selected:
          collaboratorsSelected.length === 0
            ? null
            : collaboratorsSelected.map((collaborator) =>
                Number(collaborator.id)
              ),
        group_ids_selected:
          groupsSelected.length === 0
            ? null
            : groupsSelected.map((group) => Number(group.id)),
        is_pdi: false,
        select_all_group_ids: allGroups,
        select_all_squad_ids: allSquads,
        select_all_user_ids: allCollaborators,
        group_ids_deselected:
          excludedGroups.length === 0 ? null : excludedGroups,
        squad_ids_deselected:
          excludedSquads.length === 0 ? null : excludedSquads,
        user_ids_deselected:
          excludedCollaborators.length === 0 ? null : excludedCollaborators,
      }),
    onSuccess: () => {
      alert({
        title: 'Trilha de aprendizado criada com sucesso!',
        description: 'A trilha foi criada com sucesso.',
        alertType: 'success',
      })
      router.push('/trilhas-de-aprendizagem')
    },
    onError: () => {
      alert({
        title: 'Erro ao criar trilha de aprendizado',
        description:
          'Ocorreu um erro ao criar a trilha de aprendizado. Tente novamente mais tarde.',
        alertType: 'danger',
      })
    },
  })

  const handleSubmit = (data: FormStudyPlanType) => {
    createStudyPlanMutation(data)
  }

  return (
    <StudyPlanQueriesProvider>
      <div className="m-auto flex w-full flex-col gap-4 px-2 py-4 lg:p-4">
        <div>
          <h1 className="text-ctx-content-title ts-heading-md">
            Trilha de aprendizagem
          </h1>
          <span className="text-ctx-content-base ts-paragraph-xs">
            Gerencie, acompanhe e analise o progresso de desenvolvimento dos
            colaboradores em tempo real.
          </span>
        </div>

        <FormStudyPlan handleSubmitForm={handleSubmit} />
      </div>
    </StudyPlanQueriesProvider>
  )
}
