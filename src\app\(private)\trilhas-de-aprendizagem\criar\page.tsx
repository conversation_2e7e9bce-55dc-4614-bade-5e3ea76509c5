/* eslint-disable @typescript-eslint/no-explicit-any */
'use client'

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  StepperI<PERSON>,
  useStepper,
} from '@ads/components-react'
import { zodResolver } from '@hookform/resolvers/zod'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'
import { FormProvider, useForm } from 'react-hook-form'
import { useMutation } from 'react-query'
import Cookies from 'universal-cookie'

import { createNewStudyPlan } from '@/http/study-plan'
import { useSelectedCollaboratorsTrail } from '@/store/useSelectedCollaboratorsTrail'
import { useSelectedCoursesTrail } from '@/store/useSelectedCoursesTrail'
import { useSelectedGroupsTrail } from '@/store/useSelectedGroupsTrail'
import { useSelectedSquadsTrail } from '@/store/useSelectedSquadsTrail'
import { useStepOneSaved } from '@/store/useStepOneSaved'
import { cookieSettings } from '@/utils/cookie-settings'

import { CancelNewStudyPlanAlert } from './components/cancel-alert'
import { Step1, Step2, Step3, Step4 } from './index'
import { CombinedCheckoutSchema, CombinedCheckoutType } from './validations'

export default function CriarTrilha() {
  const router = useRouter()
  const cookies = new Cookies(null, { path: '/' })

  const { activeStep, previousStep, nextStep } = useStepper({
    initialStep: 0,
    stepAmount: 4,
  })

  const step1 = cookies.get('step1')

  const coursesSelected = useSelectedCoursesTrail(
    (state) => state.coursesSelected
  )
  const { setData } = useStepOneSaved()
  const { squadsSelected, allSquads, excludedSquads } = useSelectedSquadsTrail()
  const { allGroups, excludedGroups, groupsSelected } = useSelectedGroupsTrail()
  const { collaboratorsSelected, allCollaborators, excludedCollaborators } =
    useSelectedCollaboratorsTrail()

  const methods = useForm<CombinedCheckoutType>({
    resolver: zodResolver(CombinedCheckoutSchema),
    defaultValues: {
      name: step1?.name || '',
      description: step1?.description || '',
      isMandatory: step1?.isMandatory || false,
      duration: step1?.duration || '',
      startDate: step1?.startDate || '',
      endDate: step1?.endDate || '',
    },
  })

  const { mutate: createStudyPlanMutation } = useMutation({
    mutationFn: (data: CombinedCheckoutType) => createNewStudyPlan(data as any),
    onSuccess: () => {
      alert({
        title: 'Trilha de aprendizado criada com sucesso!',
        description: 'A trilha foi criada com sucesso.',
        alertType: 'success',
      })
      cookies.remove('step1')
      cookies.remove('step2')
      cookies.remove('step3')
      router.push('/trilhas-de-aprendizagem')
    },
    onError: () => {
      alert({
        title: 'Erro ao criar trilha de aprendizado',
        description:
          'Ocorreu um erro ao criar a trilha de aprendizado. Tente novamente mais tarde.',
        alertType: 'danger',
      })
    },
  })
  console.log(methods.formState.errors)

  const handleSubmit = (data: CombinedCheckoutType) => {
    if (activeStep === 0 && !!methods.formState.errors) {
      setData({
        name: data.name,
        description: data.description || '',
        isMandatory: !!data.isMandatory,
        duration: Number(data.duration) || 0,
        startDate: new Date(data.startDate as Date),
        endDate: new Date(data.endDate as Date),
      })
      cookies.set('step1', JSON.stringify(data), cookieSettings())
      nextStep()
      return
    }

    if (activeStep === 1) {
      cookies.set('step2', coursesSelected, cookieSettings())
      nextStep()
      return
    }

    if (activeStep === 2) {
      console.log({
        collaborators: {
          all: allCollaborators,
          excluded: excludedCollaborators,
          selected: collaboratorsSelected,
        },
        squads: {
          all: allSquads,
          excluded: excludedSquads,
          selected: squadsSelected,
        },
        groups: {
          all: allGroups,
          excluded: excludedGroups,
          selected: groupsSelected,
        },
      })
      cookies.set(
        'step3',
        {
          collaborators: {
            all: allCollaborators,
            excluded: excludedCollaborators,
            selected: collaboratorsSelected,
          },
          squads: {
            all: allSquads,
            excluded: excludedSquads,
            selected: squadsSelected,
          },
          groups: {
            all: allGroups,
            excluded: excludedGroups,
            selected: groupsSelected,
          },
        },
        cookieSettings()
      )
      nextStep()
      return
    }

    if (activeStep === 3) {
      createStudyPlanMutation(data)
    }
  }

  useEffect(() => {
    const beforeUnload = (event: BeforeUnloadEvent) => {
      event.preventDefault()
      cookies.remove('step1')
      cookies.remove('step2')
      cookies.remove('step3')
    }

    window.addEventListener('beforeunload', beforeUnload)

    return () => {
      window.removeEventListener('beforeunload', beforeUnload)
    }
  }, [])

  return (
    <FormProvider {...methods}>
      <form
        className="m-auto flex w-full flex-col gap-8 px-2 py-4 lg:p-4"
        onSubmit={methods.handleSubmit(handleSubmit)}
      >
        <div>
          <h1 className="text-ctx-content-title ts-heading-md">
            Trilha de aprendizagem
          </h1>
          <span className="text-ctx-content-base ts-paragraph-xs">
            Gerencie, acompanhe e analise o progresso de desenvolvimento dos
            colaboradores em tempo real.
          </span>
        </div>
        <div className="flex w-full justify-center">
          <Stepper activeStep={activeStep}>
            <StepperItem title="Informações" />
            <StepperItem title="Selecionar cursos" />
            <StepperItem title="Selecionar participantes" />
            <StepperItem title="Resumo" />
          </Stepper>
        </div>
        <div>
          <StepperContent activeStep={activeStep} index={0}>
            <Step1 />
          </StepperContent>
          <StepperContent activeStep={activeStep} index={1}>
            <Step2 />
          </StepperContent>
          <StepperContent activeStep={activeStep} index={2}>
            <Step3 />
          </StepperContent>
          <StepperContent activeStep={activeStep} index={3}>
            <Step4 />
          </StepperContent>
        </div>
        <div className="flex w-full justify-end gap-2">
          {activeStep === 0 ? (
            <CancelNewStudyPlanAlert />
          ) : (
            <Button type="button" hierarchy="tertiary" onClick={previousStep}>
              Voltar
            </Button>
          )}

          <Button type="submit">Próximo</Button>
        </div>
      </form>
    </FormProvider>
  )
}
