import Cookies from 'universal-cookie'
import { create } from 'zustand'

interface CompanyState {
  currentEnrollmentId: string
  totalLicenses: number
  totalUserPerLicense: number
  currentEnrollment: { label: string; value: string }
  companyId: number

  handleUpdateCurrentEnrollmentId: (companyId: string) => void
  handleUpdateTotalUserPerLicense: (total: number) => void
  handleUpdateTotalLicenses: (total: number) => void
  changeCurrentEnrollmentId: (id: number) => void
  setCurrentEnrollment: (enrollment: { label: string; value: string }) => void
  setCompanyId: (id: number) => void
  initializeStore: (user?: {
    metadata?: { company_id?: string | number }
  }) => void
}

const cookies = new Cookies(null, { path: '/' })

export const useCompanyStore = create<CompanyState>((set) => ({
  currentEnrollmentId: String(cookies.get('b2bEnrollmentId')) || '',
  totalLicenses: 0,
  totalUserPerLicense: 0,
  currentEnrollment: { label: '', value: '' },
  companyId: Number(cookies.get('b2bCompanyId')) || 0,

  handleUpdateTotalUserPerLicense: (total: number) => {
    set({ totalUserPerLicense: total })
  },

  changeCurrentEnrollmentId: (id: number) => {
    set({ currentEnrollmentId: String(id) })
  },

  handleUpdateTotalLicenses: (total: number) => {
    set({ totalLicenses: total })
  },

  handleUpdateCurrentEnrollmentId: (b2bEnrollmentId: string) => {
    set({ currentEnrollmentId: b2bEnrollmentId })

    const event = new CustomEvent('invalidateEnrollmentQueries')
    window.dispatchEvent(event)
  },

  setCurrentEnrollment: (enrollment: { label: string; value: string }) => {
    set({ currentEnrollment: enrollment })
  },

  setCompanyId: (id: number) => {
    set({ companyId: id })
  },

  initializeStore: (user?: { metadata?: { company_id?: string | number } }) => {
    const companyIdFromUser = Number(user?.metadata?.company_id)
    const companyIdFromCookie = Number(cookies.get('b2bCompanyId'))
    const enrollmentIdFromCookie = String(cookies.get('b2bEnrollmentId'))

    set({
      companyId: companyIdFromUser || companyIdFromCookie || 0,
      currentEnrollmentId: enrollmentIdFromCookie || '',
    })
  },
}))
