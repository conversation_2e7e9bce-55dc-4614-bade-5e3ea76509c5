{"name": "plataforma-solution-b2b", "version": "0.1.0", "private": true, "packageManager": "pnpm@10.13.1", "scripts": {"prepare": "husky", "dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "codegen": "graphql-codegen --config codegen.ts", "format": "prettier --write ."}, "dependencies": {"@ads/components-react": "^1.1.16", "@ads/tokens": "^1.5.0", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.23.3", "graphql": "^16.11.0", "graphql-request": "^7.2.0", "jsonwebtoken": "^9.0.2", "jwt": "^0.2.0", "lucide-react": "^0.525.0", "next": "15.3.4", "next-themes": "^0.4.6", "react": "^19.0.0", "react-day-picker": "^9.8.1", "react-dom": "^19.0.0", "react-hook-form": "^7.59.0", "react-icons": "^5.5.0", "react-query": "^3.39.3", "react-resizable-panels": "^3.0.4", "recharts": "2.15.4", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "tw-colors": "^3.3.2", "universal-cookie": "^8.0.1", "zod": "^3.25.73", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@eslint/js": "^9.30.1", "@graphql-codegen/cli": "5.0.7", "@graphql-codegen/client-preset": "4.8.3", "@graphql-typed-document-node/core": "^3.2.0", "@tailwindcss/container-queries": "^0.1.1", "@tailwindcss/postcss": "^4", "@tailwindcss/typography": "^0.5.16", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.4", "eslint-config-prettier": "^10.1.5", "eslint-plugin-next": "^0.0.0", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-simple-import-sort": "^12.1.1", "globals": "^16.3.0", "husky": "^9.1.7", "lint-staged": "^16.1.2", "postcss": "^8.5.6", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^3.4.17", "typescript": "^5", "typescript-eslint": "^8.35.1"}}