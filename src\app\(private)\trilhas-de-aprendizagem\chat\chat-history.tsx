import { useQuery } from 'react-query'

import { ChatHistorySkeleton } from '@/components/loaders/skeletons/chat-history'
import { ScrollArea } from '@/components/ui/scroll-area'
import { getChatHistory } from '@/http/chats'
import { convertDate } from '@/utils/convert-date'

import { ChatList } from './chat-history-list'

interface ChatHistoryProps {
  onChatSelect?: () => void
}

export function ChatHistory({ onChatSelect }: ChatHistoryProps) {
  const { data, isLoading } = useQuery({
    queryKey: ['history-chats'],
    queryFn: () => getChatHistory(),
  })

  if (isLoading) {
    return <ChatHistorySkeleton />
  }

  return (
    <ScrollArea className="flex flex-col bg-ctx-layout-body px-4 md:h-[calc(100vh-64px)]">
      <div className="flex flex-col space-y-2">
        {data && data?.today.length > 0 && (
          <ChatList
            label="Hoje"
            chats={data.today}
            onChatSelect={onChatSelect}
          />
        )}

        {data && data?.yesterday.length > 0 && (
          <ChatList
            label="Ontem"
            chats={data.yesterday}
            onChatSelect={onChatSelect}
          />
        )}

        {data && data?.lastweek.length > 0 && (
          <ChatList
            label="Semana passada"
            chats={data.lastweek}
            onChatSelect={onChatSelect}
          />
        )}

        {data &&
          Object.entries(data.pastmonths).length > 0 &&
          Object.entries(data.pastmonths).map(([month, chats]) => (
            <ChatList
              key={month}
              label={convertDate(new Date(month))}
              chats={chats}
              onChatSelect={onChatSelect}
            />
          ))}
      </div>
    </ScrollArea>
  )
}
