'use client'

import {
  <PERSON>bMenu,
  TabMenuContent,
  TabMenuList,
  TabMenuTrigger,
} from '@ads/components-react'
import { useEffect, useState } from 'react'
import {
  HiOutlineUser,
  HiOutlineUserGroup,
  HiOutlineUsers,
} from 'react-icons/hi2'
import { useQuery } from 'react-query'
import Cookies from 'universal-cookie'

import { CollaboratorsTable } from '@/components/tables/study-plan/collaborators'
import { GroupsTable } from '@/components/tables/study-plan/groups'
import { SquadsTable } from '@/components/tables/study-plan/squads'
import { EListOrder, EListOrderBy } from '@/graphql/generated/graphql'
import { useDebouncedValue } from '@/hooks/use-debounce'
import { getCollaborators } from '@/http/collaborators/get-collaborators'
import { getGroups } from '@/http/groups'
import { getTeams } from '@/http/teams/get-teams'

import { EmptyState } from '../../components/empty-state'
import { SearchCheck } from './search'

export function Step3() {
  const [currentPage, setCurrentPage] = useState(1)
  const [searchValue, setSearchValue] = useState('')

  const [currentTab, setCurrentTab] = useState('tab1')

  const debouncedSearchValue = useDebouncedValue(searchValue, 500)

  const cookies = new Cookies(null, { path: '/' })

  const companyId = cookies.get('b2bCompanyId')
  const enrollmentId = Number(cookies.get('b2bEnrollmentId'))

  const {
    data: collaborators,
    isFetching,
    isLoading,
  } = useQuery({
    queryKey: [
      'getCollaboratorsGql',
      debouncedSearchValue,
      currentPage,
      companyId,
      enrollmentId,
    ],
    queryFn: () =>
      getCollaborators({
        page: currentPage,
        limit: 10,
        company_id: companyId,
        enrollment_id: enrollmentId,
        order: EListOrder.ASC,
        orderBy: EListOrderBy.NAME,
        q: debouncedSearchValue ?? '',
      }),
    refetchOnWindowFocus: false,
    staleTime: 1000 * 60 * 5,
  })

  const {
    data: teams,
    isFetching: isFetchingTeams,
    isLoading: isLoadingTeams,
  } = useQuery({
    queryKey: ['getTeamsGql', debouncedSearchValue, currentPage, companyId],
    queryFn: () =>
      getTeams({
        title: debouncedSearchValue ?? '',
        company_id: companyId,
        page: currentPage,
        limit: 10,
      }),
    refetchOnWindowFocus: false,
    staleTime: 1000 * 60 * 5,
  })

  const {
    data: groups,
    isFetching: isFetchingGroups,
    isLoading: isLoadingGroups,
  } = useQuery({
    queryKey: ['getGroupsGql', debouncedSearchValue, currentPage, companyId],
    queryFn: () =>
      getGroups({
        name: debouncedSearchValue ?? '',
        company_id: companyId,
        page: currentPage,
        limit: 10,
      }),

    refetchOnWindowFocus: false,
    staleTime: 1000 * 60 * 5,
  })

  useEffect(() => {
    setCurrentPage(1)
  }, [debouncedSearchValue, setCurrentPage])

  useEffect(() => {
    setSearchValue('')
  }, [currentTab])

  return (
    <div className="space-y-3 rounded-lg bg-ctx-layout-body p-4 lg:p-8">
      <div className="flex justify-center">
        <TabMenu
          className="relative w-full"
          value={currentTab}
          onValueChange={setCurrentTab}
        >
          <div className="flex justify-center">
            <TabMenuList className="bg-ctx-layout-surface">
              <TabMenuTrigger value="tab1" leadingIcon={HiOutlineUser}>
                Colaboradores
              </TabMenuTrigger>
              <TabMenuTrigger value="tab2" leadingIcon={HiOutlineUsers}>
                Equipes
              </TabMenuTrigger>
              <TabMenuTrigger value="tab3" leadingIcon={HiOutlineUserGroup}>
                Grupos
              </TabMenuTrigger>
            </TabMenuList>
          </div>
          <TabMenuContent className="mt-8 w-full" value="tab1">
            <SearchCheck
              title="Selecionar todos os colaboradores"
              placeholder="Buscar por nome ou e-mail"
              currentTab={currentTab}
              setSearchValue={setSearchValue}
              searchValue={searchValue}
            />

            <div className="mt-6">
              {collaborators?.users.data.length === 0 &&
              !isLoading &&
              !isFetching ? (
                <EmptyState />
              ) : (
                <CollaboratorsTable
                  isLoading={isLoading}
                  isFetchingNewPage={isFetchingTeams}
                  currentPage={currentPage}
                  setCurrentPage={setCurrentPage}
                  collaborators={
                    collaborators?.users ?? {
                      data: [],
                      total: 0,
                      perPage: 0,
                    }
                  }
                />
              )}
            </div>
          </TabMenuContent>
          <TabMenuContent className="mt-8 w-full" value="tab2">
            <SearchCheck
              title="Selecionar todas as equipes"
              placeholder="Buscar por nome"
              currentTab={currentTab}
              setSearchValue={setSearchValue}
              searchValue={searchValue}
            />

            <div className="mt-6">
              {teams?.companySquads?.data.length === 0 &&
              !isLoadingTeams &&
              !isFetchingTeams ? (
                <EmptyState />
              ) : (
                <SquadsTable
                  isLoading={isLoadingTeams}
                  isFetchingNewPage={isFetchingTeams}
                  currentPage={currentPage}
                  setCurrentPage={setCurrentPage}
                  squads={
                    teams?.companySquads ?? {
                      data: [],
                      total: 0,
                      perPage: 0,
                    }
                  }
                />
              )}
            </div>
          </TabMenuContent>
          <TabMenuContent className="mt-8 w-full" value="tab3">
            <SearchCheck
              title="Selecionar todos os grupos"
              placeholder="Buscar por nome"
              currentTab={currentTab}
              setSearchValue={setSearchValue}
              searchValue={searchValue}
            />

            <div className="mt-6">
              {groups?.companyGroups?.data.length === 0 &&
              !isLoadingGroups &&
              !isFetchingGroups ? (
                <EmptyState />
              ) : (
                <GroupsTable
                  isLoading={isLoadingGroups}
                  isFetchingNewPage={isFetchingGroups}
                  currentPage={currentPage}
                  setCurrentPage={setCurrentPage}
                  groups={
                    groups?.companyGroups ?? {
                      data: [],
                      total: 0,
                      perPage: 0,
                    }
                  }
                />
              )}
            </div>
          </TabMenuContent>
        </TabMenu>
      </div>
    </div>
  )
}
