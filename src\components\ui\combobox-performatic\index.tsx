import { Checkbox } from '@ads/components-react'
import { Loader2 } from 'lucide-react'
import * as React from 'react'
import { FixedSizeList as List } from 'react-window'

import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { LabelAndValue } from '@/model/labelAndValue'

import { Command, CommandInput } from '../command'
import { InputField } from '../input-form'
import { ListItem } from './lists'

type MultiselectComboboxProps = {
  label?: string
  placeholder?: string
  searchPlaceholder?: string
  emptyMessage?: string
  loadingMessage?: string
  options: LabelAndValue[]
  value?: LabelAndValue[]
  onChange: (value: LabelAndValue[]) => void
  errorMessage?: string
  disabled?: boolean
  isLoading?: boolean
  maxSelectedDisplay?: number
  className?: string
  selectAllLabel?: string
  clearAllLabel?: string
  showSelectAll?: boolean
  showClearAll?: boolean
  itemHeight?: number
  maxHeight?: number
}

export function PerformaticCombobox({
  label,
  placeholder = 'Selecione opções...',
  searchPlaceholder = 'Pesquisar...',
  emptyMessage = 'Não encontrado.',
  loadingMessage = 'Carregando...',
  options,
  value = [],
  onChange,
  errorMessage,
  disabled = false,
  isLoading = false,
  maxSelectedDisplay = 3,
  selectAllLabel = 'Selecionar todas',
  showSelectAll = true,
  showClearAll = true,
  itemHeight = 32,
  maxHeight = 300,
}: MultiselectComboboxProps) {
  const [open, setOpen] = React.useState(false)
  const [searchValue, setSearchValue] = React.useState('')

  const selectedValues = React.useMemo(
    () => new Set(value.map((item) => item.value)),
    [value]
  )

  const filteredOptions = React.useMemo(() => {
    if (!searchValue) return options
    return options.filter((option) =>
      option.label.toLowerCase().includes(searchValue.toLowerCase())
    )
  }, [options, searchValue])

  const isAllSelected = React.useMemo(() => {
    return (
      filteredOptions.length > 0 &&
      filteredOptions.every((option) => selectedValues.has(option.value))
    )
  }, [filteredOptions, selectedValues])

  const handleSelect = React.useCallback(
    (option: LabelAndValue) => {
      const newValue = selectedValues.has(option.value)
        ? value.filter((item) => item.value !== option.value)
        : [...value, option]
      onChange(newValue)
    },
    [value, selectedValues, onChange]
  )

  const handleSelectAll = React.useCallback(() => {
    if (isAllSelected) {
      const filteredValues = new Set(filteredOptions.map((opt) => opt.value))
      const newValue = value.filter((item) => !filteredValues.has(item.value))
      onChange(newValue)
    } else {
      const newItems = filteredOptions.filter(
        (option) => !selectedValues.has(option.value)
      )
      onChange([...value, ...newItems])
    }
  }, [isAllSelected, filteredOptions, value, selectedValues, onChange])

  const displayText = React.useMemo(() => {
    if (value.length === 0) return placeholder
    if (value.length <= maxSelectedDisplay) {
      return value.map((item) => item.label).join(', ')
    }
    return `${value
      .slice(0, maxSelectedDisplay)
      .map((item) => item.label)
      .join(', ')} +${value.length - maxSelectedDisplay} mais`
  }, [value, maxSelectedDisplay, placeholder])

  const listData = React.useMemo(
    () => ({
      items: filteredOptions,
      selectedValues,
      onSelect: handleSelect,
    }),
    [filteredOptions, selectedValues, handleSelect]
  )

  const listHeight = Math.min(
    maxHeight,
    filteredOptions.length * itemHeight +
      (showSelectAll || showClearAll ? itemHeight : 0)
  )

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger disabled={disabled} className="text-start">
        <InputField
          label={label}
          value={displayText}
          errorMessage={errorMessage}
          disabled={disabled || isLoading}
          placeholder={placeholder}
          custom={{
            input:
              'h-10 w-full cursor-pointer truncate text-start text-ctx-content-base',
          }}
        />
      </PopoverTrigger>

      <PopoverContent
        className="w-[--radix-popover-trigger-width] rounded-lg bg-ctx-layout-spotlight p-1"
        align="start"
      >
        <Command>
          <CommandInput
            placeholder={searchPlaceholder}
            value={searchValue}
            onValueChange={(e) => setSearchValue(e)}
            className="w-full rounded-md border-0 px-2 outline-none"
          />
          <div className="flex flex-col">
            <div className="flex flex-col">
              {isLoading ? (
                <div className="flex items-center justify-center p-4">
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  <span className="text-paragraph-xxs text-ctx-content-base">
                    {loadingMessage}
                  </span>
                </div>
              ) : filteredOptions.length === 0 ? (
                <div className="text-paragraph-xxs p-4 text-center text-ctx-content-base">
                  {emptyMessage}
                </div>
              ) : (
                <>
                  {showSelectAll &&
                    filteredOptions.length > 0 &&
                    !searchValue && (
                      <div className="border-ctx-layout-border px-2 pb-1 pt-2">
                        <Checkbox
                          checked={isAllSelected}
                          custom={{ item: 'bg-ctx-interactive-secondary' }}
                          label={selectAllLabel}
                          onCheckedChange={handleSelectAll}
                        />
                      </div>
                    )}

                  <List
                    height={listHeight}
                    itemCount={filteredOptions.length}
                    itemSize={itemHeight}
                    itemData={listData}
                    width="100%"
                  >
                    {ListItem}
                  </List>
                </>
              )}
            </div>
          </div>
        </Command>
      </PopoverContent>
    </Popover>
  )
}
