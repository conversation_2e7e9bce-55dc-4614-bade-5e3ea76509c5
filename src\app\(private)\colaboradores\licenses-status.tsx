import { UserMinus, UserPlus } from 'lucide-react'
import { useMemo } from 'react'
import { useQuery } from 'react-query'
import Cookies from 'universal-cookie'

import { LicensesStatusSkeleton } from '@/components/loaders/skeletons/licenses-status'
import { Progress } from '@/components/ui/progress'
import { getLicensesCount } from '@/http/collaborators/get-licenses-count'

export function LicensesStatus() {
  const { enrollmentId } = useMemo(() => {
    const cookies = new Cookies(null, { path: '/' })
    return {
      companyId: cookies.get('b2bCompanyId'),
      enrollmentId: Number(cookies.get('b2bEnrollmentId')),
    }
  }, [])

  const { data: licensesCount, isLoading: isLoadingLicensesCount } = useQuery({
    queryKey: ['getLicensesCount', enrollmentId],
    queryFn: () => getLicensesCount(enrollmentId),
    enabled: !!enrollmentId,
    refetchOnWindowFocus: false,
  })

  const licensesUtilized =
    licensesCount?.enrollment?.b2b_metadata?.employees_count || 0
  const licensesAvailable =
    licensesCount?.enrollment?.b2b_metadata?.licenses_count || 0
  const licensesPercent = (
    (licensesUtilized / licensesAvailable) *
    100
  ).toFixed(0)

  if (isLoadingLicensesCount || !licensesCount) {
    return <LicensesStatusSkeleton />
  }

  return (
    <div className="flex flex-col justify-between gap-6 rounded-xl bg-ctx-layout-body px-6 py-2 sm:flex-row sm:items-center">
      <div className="flex-1 space-y-1">
        <p className="text-ctx-content-title ts-paragraph-xxs">
          Licenças utilizadas: {licensesUtilized}/{licensesAvailable}
        </p>
        <div className="flex items-center gap-2">
          <Progress
            value={Number(licensesPercent)}
            className="w-full bg-ctx-layout-surface md:max-w-sm"
          />
          <p className="text-ctx-content-title ts-paragraph-xxxs">
            {licensesPercent}%
          </p>
        </div>
      </div>
      <div className="hidden items-center gap-6 sm:flex">
        <p className="flex items-center gap-2 ts-heading-xs">
          <UserPlus /> {licensesUtilized} Utilizadas
        </p>
        <p className="flex items-center gap-2 ts-heading-xs">
          <UserMinus /> {licensesAvailable - licensesUtilized} Disponíveis
        </p>
      </div>
    </div>
  )
}
