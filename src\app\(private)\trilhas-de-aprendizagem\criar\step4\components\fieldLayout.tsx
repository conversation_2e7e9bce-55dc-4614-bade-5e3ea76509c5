import { InputField } from '@/components/ui/input-form'

type FieldLayoutProps = {
  title: string
  defaultValue: string | number
}

export function FieldLayout({ title, defaultValue }: FieldLayoutProps) {
  return (
    <section className="space-y-2 rounded-xl border border-ctx-layout-border p-4">
      <InputField
        readOnly
        id="name"
        label={title}
        type="text"
        className="w-full"
        defaultValue={defaultValue as string}
      />
    </section>
  )
}
