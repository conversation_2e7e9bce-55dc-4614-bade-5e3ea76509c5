import { InputField } from '@/components/ui/input-form'

import { List } from './list'

type FieldLayoutProps = {
  title: string
  defaultValue:
    | string
    | number
    | {
        collaborators: number
        teams: number
        groups: number
      }
  isTextArea?: boolean
}

export function FieldLayout({
  title,
  defaultValue,
  isTextArea,
}: FieldLayoutProps) {
  return (
    <section className="space-y-2 rounded-xl border border-ctx-layout-border p-4">
      {isTextArea ? (
        <div className="space-y-1.5">
          <span className="text-ctx-content-title ts-heading-xxs">{title}</span>
          {defaultValue.collaborators && (
            <List
              collaborators={defaultValue?.collaborators}
              teams={defaultValue?.teams}
              groups={defaultValue?.groups}
            />
          )}
          <List
            collaborators={defaultValue?.collaborators}
            teams={defaultValue?.teams}
            groups={defaultValue?.groups}
          />
        </div>
      ) : (
        <InputField
          readOnly
          id="name"
          label={title}
          type="text"
          className="w-full"
          defaultValue={defaultValue as string}
        />
      )}
    </section>
  )
}
