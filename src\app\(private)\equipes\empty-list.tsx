'use client'
import { IconShape } from '@ads/components-react'
import { Search } from 'lucide-react'

interface EmptyListProps {
  title?: string
  description?: string
}

export function EmptyList({
  title = 'Sem Equipes',
  description = 'Você ainda não criou nenhuma Equipe.',
}: EmptyListProps) {
  return (
    <div className="mx-auto flex h-[calc(100vh-310px)] w-full flex-col items-center justify-center gap-4 rounded-2xl bg-ctx-layout-body">
      <IconShape
        className="bg-ctx-highlight-promote"
        type="brand"
        size="md"
        icon={() => <Search size={28} className="text-base-gray-900" />}
      />
      <div>
        <h4 className="text-center text-ctx-content-title ts-heading-xs">
          {title}
        </h4>
        <p className="mt-1 text-center text-ctx-content-base ts-paragraph-xxs">
          {description}
        </p>
      </div>
    </div>
  )
}
