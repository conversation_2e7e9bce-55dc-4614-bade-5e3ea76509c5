import { GET_LICENSES_COUNT } from '@/graphql/collaborators/queries'
import { GetLicensesCountQuery } from '@/graphql/generated/graphql'
import { clientGraphql } from '@/services/graphql'

export const getLicensesCount = async (enrollmentId: number) =>
  clientGraphql.request<GetLicensesCountQuery>(
    GET_LICENSES_COUNT,
    { enrollment_id: enrollmentId },
    {
      'cache-control': 'no-cache',
    }
  )
