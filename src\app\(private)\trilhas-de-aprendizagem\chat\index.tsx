'use client'

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Toolt<PERSON> } from '@ads/components-react'
import {
  ArrowLeftIcon,
  ArrowRightIcon,
  ClockIcon,
  MessageCircleIcon,
  X,
} from 'lucide-react'
import { useState } from 'react'

import { ToggleSwitch } from '@/components/toggle-switch'
import { Separator } from '@/components/ui/separator'
import { useChatStore } from '@/store/useChatStore'

import { ChatAI } from './chat-ai'
import { ChatForm } from './chat-form'
import { ChatHistory } from './chat-history'

interface ChatPanelProps {
  isExpanded: boolean
  chatIdFromQuery?: string | null
  onExpandToggle: () => void
  onClose: () => void
}

export function ChatPanel({
  isExpanded,
  onExpandToggle,
  onClose,
}: ChatPanelProps) {
  const { chatId } = useChatStore()

  const [activeTab, setActiveTab] = useState<string>('chat')

  return (
    <div className="flex h-[calc(100vh-64px)] flex-col">
      <header className="hidden space-y-2 bg-ctx-layout-body p-4 md:block">
        <section className="flex items-center justify-between space-x-2">
          <div className="flex items-center gap-3">
            <Tooltip
              key={isExpanded ? 'Recolher' : 'Expandir'}
              side="right"
              title={isExpanded ? 'Recolher' : 'Expandir'}
            >
              <IconButton
                size="sm"
                ariaLabel="Recolher"
                hierarchy="secondary"
                icon={isExpanded ? ArrowRightIcon : ArrowLeftIcon}
                onClick={onExpandToggle}
              />
            </Tooltip>

            <div>
              {activeTab === 'chat' ? (
                <>
                  <h1 className="text-ctx-content-title ts-heading-sm">
                    Planejar trilha
                  </h1>
                </>
              ) : (
                <h1 className="text-ctx-content-title ts-heading-sm">
                  Histórico do chat
                </h1>
              )}
            </div>
          </div>

          <div className="flex items-center space-x-4">
            <ToggleSwitch
              options={[
                {
                  value: 'chat',
                  icon: <MessageCircleIcon size={14} />,
                  tooltip: 'Chat IA',
                },
                {
                  value: 'history',
                  icon: <ClockIcon size={14} />,
                  tooltip: 'Histórico',
                },
              ]}
              defaultValue={activeTab}
              onChange={(value) => {
                setActiveTab(value)
              }}
            />

            <IconButton
              size="sm"
              ariaLabel="fechar"
              hierarchy="secondary"
              icon={X}
              onClick={onClose}
            />
          </div>
        </section>

        <Separator />
      </header>

      <header className="block space-y-1 bg-ctx-layout-body p-4 md:hidden">
        <section className="flex items-center justify-between">
          <div>
            {activeTab === 'chat' ? (
              <>
                <h1 className="text-ctx-content-title ts-heading-sm">
                  Planejar trilha
                </h1>
              </>
            ) : (
              <h1 className="text-ctx-content-title ts-heading-sm">
                Histórico do chat
              </h1>
            )}
          </div>

          <div className="flex items-center space-x-4">
            <ToggleSwitch
              options={[
                { value: 'chat', icon: <MessageCircleIcon size={14} /> },
                { value: 'history', icon: <ClockIcon size={14} /> },
              ]}
              defaultValue={activeTab}
              onChange={(value) => {
                setActiveTab(value)
              }}
            />

            <IconButton
              size="sm"
              ariaLabel="fechar"
              hierarchy="secondary"
              icon={X}
              onClick={onClose}
            />
          </div>
        </section>

        <Separator />
      </header>

      {activeTab === 'chat' ? (
        chatId ? (
          <ChatAI />
        ) : (
          <ChatForm />
        )
      ) : (
        <ChatHistory onChatSelect={() => setActiveTab('chat')} />
      )}
    </div>
  )
}
