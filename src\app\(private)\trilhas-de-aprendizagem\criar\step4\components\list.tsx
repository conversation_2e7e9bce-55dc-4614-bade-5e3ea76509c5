import { ScrollArea } from '@/components/ui/scroll-area'

type ListProps = {
  collaborators: number
  teams: number
  groups: number
}

export function List({ collaborators, teams, groups }: ListProps) {
  return (
    <ScrollArea className="h-24 w-full rounded-lg border border-border bg-ctx-interactive-secondary p-2 px-4 text-ctx-content-base">
      <ul className="space-y-1.5 ts-heading-xxs">
        <li className="font-extrabold">Colaboradores: {collaborators}</li>

        <li className="font-extrabold">Equipes: {teams}</li>

        <li className="font-extrabold">Grupos: {groups}</li>
      </ul>
    </ScrollArea>
  )
}
