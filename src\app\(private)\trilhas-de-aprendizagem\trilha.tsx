'use client'

import {
  Button,
  Dropdown,
  DropdownItem,
  DropdownItemContent,
  Pagination,
} from '@ads/components-react'
import { Plus } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useState } from 'react'
import { HiOutlineSparkles } from 'react-icons/hi2'
import { useQuery } from 'react-query'
import { useStore } from 'zustand'

import { Filter } from '@/components/filter/filter'
import { PageLayout } from '@/components/layouts/page-layout'
import { CardsSkeleton } from '@/components/loaders/skeletons/cards'
import { ScrollArea } from '@/components/ui/scroll-area'
import { useAuth } from '@/contexts/AuthContext'
import { getStudyPlans } from '@/http/study-plan'
import { StudyPlanData } from '@/model/study-plan'
import { useFilterStore } from '@/store/useFilterStore'

import { CardInfo } from './components/card'
import { EmptyState } from './components/empty-state'

type TrilhasProps = {
  isExpanded: boolean
  setIsExpanded: () => void
}

export function Trilhas({ setIsExpanded }: TrilhasProps) {
  const router = useRouter()
  const { user } = useAuth()
  const { search, status } = useStore(useFilterStore)
  const { onChangeSearch, onChangeStatus } = useFilterStore()
  const [currentPage, setCurrentPage] = useState(1)

  const handleStatus = (): boolean | null => {
    if (status === 'INACTIVE') return false
    if (status === 'FINISHED') return true
    if (status === 'ACTIVE') return true

    return true
  }

  const {
    data: studyPlans,
    isLoading,
    isFetching,
  } = useQuery({
    queryKey: ['getStudyPlansGql', currentPage, user, search, status],
    queryFn: () =>
      getStudyPlans({
        page: currentPage,
        company_id: user?.metadata.company_id ?? 0,
        limit: 12,
        name: search ?? '',
        onlyPDI: false,
        user_id: null,
        end_date: status === 'FINISHED' ? new Date() : null,
        status: handleStatus(),
      }),
    refetchOnWindowFocus: false,
  })

  return (
    <PageLayout
      title="Trilhas de aprendizagem"
      description="Gerencie, acompanhe e analise o progresso de desenvolvimento dos colaboradores em tempo real."
      className="h-[calc(100vh-64px)]"
      actionButton={
        <Dropdown
          trigger={
            <Button trailingIcon={Plus} className="w-full sm:w-fit">
              Nova Trilha
            </Button>
          }
        >
          <div className="flex flex-col gap-4 p-2">
            <Button
              hierarchy="secondary"
              className="w-full"
              onClick={() => router.push('/trilhas-de-aprendizagem/criar')}
            >
              Criar trilha manualmente
            </Button>
            <DropdownItemContent>
              <DropdownItem asChild>
                <Button
                  onClick={setIsExpanded}
                  trailingIcon={HiOutlineSparkles}
                  className="w-full rounded-pill text-black"
                >
                  Criar trilha com IA
                </Button>
              </DropdownItem>
            </DropdownItemContent>
          </div>
        </Dropdown>
      }
    >
      <ScrollArea className="h-full min-w-[386px] space-y-8 rounded-md bg-ctx-layout-body px-6 pb-10 md:h-[calc(100vh-197px)] [&>div]:py-1">
        <div className="mb-8 flex items-center justify-between gap-4">
          <Filter
            pageKey="trilhas"
            placeholder="Busca por Trilha"
            onChangeStatus={onChangeStatus}
            onChangeSearch={onChangeSearch}
          />
        </div>
        <div className="flex flex-wrap gap-8">
          {isLoading || isFetching ? (
            <CardsSkeleton />
          ) : studyPlans?.studyPlans.data.length === 0 ? (
            <EmptyState />
          ) : (
            studyPlans?.studyPlans.data.map((studyPlan) => (
              <CardInfo
                key={studyPlan.id}
                studyPlanDataInfo={studyPlan as StudyPlanData}
              />
            ))
          )}
        </div>
        <div className="mt-8 flex justify-center">
          {(studyPlans?.studyPlans.data.length as number) > 0 && (
            <Pagination
              controlText={{ previous: 'Anterior', next: 'Próximo' }}
              activeIndex={currentPage - 1}
              setActiveIndex={(e) => setCurrentPage((e as number) + 1)}
              pageAmount={Math.ceil(
                (studyPlans?.studyPlans?.total as number) /
                  (studyPlans?.studyPlans?.perPage as number)
              )}
              ellipsisLabel="Buscar mais itens"
            />
          )}
        </div>
      </ScrollArea>
    </PageLayout>
  )
}
