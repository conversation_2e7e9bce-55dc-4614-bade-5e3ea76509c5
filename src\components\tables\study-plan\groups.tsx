'use client'

import { Checkbox } from '@ads/components-react'
import { useEffect } from 'react'
import Cookies from 'universal-cookie'

import {
  DataTable,
  PaginationInfo,
  TableColumn,
} from '@/components/ui/data-table/data-table'
import { GetGroupsQuery } from '@/graphql/generated/graphql'
import { GroupsData } from '@/model/study-plan'
import { useSelectedGroupsTrail } from '@/store/useSelectedGroupsTrail'

type StudyPlanTableProps = {
  groups: GetGroupsQuery['companyGroups']
  isLoading: boolean
  currentPage: number
  setCurrentPage: (page: number) => void
  isFetchingNewPage?: boolean
}

export function GroupsTable({
  groups,
  isLoading,
  currentPage,
  setCurrentPage,
  isFetchingNewPage = false,
}: StudyPlanTableProps) {
  const paginationInfo: PaginationInfo = {
    currentPage,
    totalPages: Math.ceil(groups.total / groups.perPage) || 1,
    itemsPerPage: groups.perPage,
    totalItems: groups.total,
  }

  const toggleGroup = useSelectedGroupsTrail((state) => state.toggleGroup)
  const setGroupsSelected = useSelectedGroupsTrail(
    (state) => state.setGroupsSelected
  )

  const isGroupSelecionado = useSelectedGroupsTrail(
    (state) => state.isGroupSelecionado
  )

  const cookies = new Cookies(null, { path: '/' })

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  useEffect(() => {
    const savedStep3 = cookies.get('step3')
    if (savedStep3 && typeof savedStep3 === 'object' && savedStep3.groups) {
      const { groups } = savedStep3

      setGroupsSelected(groups.selected ?? [])

      useSelectedGroupsTrail.setState({
        allGroups: groups.all ?? false,
        excludedGroups: groups.excluded ?? [],
      })
    }
  }, [])

  const columns: TableColumn<GroupsData>[] = [
    {
      key: 'status',
      header: 'Selecionar',
      accessor: (group) => {
        const groupId = Number(group.id)
        const groupName = String(group.name)

        const isChecked = isGroupSelecionado(groupId)

        return (
          <div className="flex justify-center pr-6">
            <Checkbox
              onCheckedChange={() =>
                toggleGroup({
                  id: groupId,
                  name: groupName,
                })
              }
              checked={isChecked}
              size="md"
              className="dark:rounded-sm dark:bg-[#FFFFFFB8]"
            />
          </div>
        )
      },
      headerClassName: 'text-center w-10',
      className: 'text-center',
    },

    {
      key: 'name',
      header: 'Nome do Grupo',
      accessor: 'name',
    },
  ]

  return (
    <DataTable
      data={groups.data as GroupsData[]}
      columns={columns}
      pagination={paginationInfo}
      onPageChange={handlePageChange}
      loading={isLoading}
      isFetchingNewPage={isFetchingNewPage}
      emptyMessage="Nenhum grupo encontrado."
      className="overflow-hidden p-0"
    />
  )
}
