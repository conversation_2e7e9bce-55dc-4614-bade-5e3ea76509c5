'use client'

import { IconButton } from '@ads/components-react'
import { ArrowLeftIcon } from 'lucide-react'
import { useRouter } from 'next/navigation'

import { Separator } from '@/components/ui/separator'

import { ChatAI } from './chat-ai'

export default function ChatId() {
  const { back } = useRouter()

  return (
    <div className="flex h-full w-full flex-col p-2 md:px-4 md:pt-4">
      <header className="space-y-2">
        <section className="flex items-center justify-between space-x-2">
          <div className="flex items-center space-x-2">
            <IconButton
              size="sm"
              ariaLabel="voltar"
              hierarchy="secondary"
              icon={ArrowLeftIcon}
              onClick={() => {
                back()
              }}
            />

            <h1 className="text-ctx-content-title ts-heading-sm">
              Consulte os melhores cursos
            </h1>
          </div>

          {/* TODO:validar se tera o historico */}
          {/* <div className="flex items-center space-x-4">
            <ToggleSwitch
              options={[
                { value: 'chat', icon: <MessageCircleIcon size={20} /> },
                { value: 'history', icon: <ClockIcon size={20} /> },
              ]}
              defaultValue={activeTab}
              onChange={(value) => {
                setActiveTab(value)
              }}
            />
          </div> */}
        </section>

        {/* <div>
         TODO:validar se tera o historico 
        {activeTab === 'chat' ? (
            <>
              <h1 className="text-ctx-content-title ts-heading-sm">
                Consulte os melhores cursos
              </h1>
            </>
          ) : (
            <h1 className="text-ctx-content-title ts-heading-sm">
              Histórico do chat
            </h1>
          )} }

          <h1 className="text-ctx-content-title ts-heading-sm">
            Consulte os melhores cursos
          </h1> 
        </div> */}

        <Separator />
      </header>

      <ChatAI />
    </div>
  )
}
