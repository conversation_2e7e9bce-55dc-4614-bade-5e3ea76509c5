import { Checkbox, Search } from '@ads/components-react'

import { useSelectedCollaboratorsTrail } from '@/store/useSelectedCollaboratorsTrail'
import { useSelectedGroupsTrail } from '@/store/useSelectedGroupsTrail'
import { useSelectedSquadsTrail } from '@/store/useSelectedSquadsTrail'

interface SearchCheckProps {
  searchValue: string
  setSearchValue: (value: string) => void
  placeholder: string
  currentTab: string
  title: string
}

export function SearchCheck({
  title,
  currentTab,
  placeholder,
  searchValue,
  setSearchValue,
}: SearchCheckProps) {
  const allCollaborators = useSelectedCollaboratorsTrail(
    (state) => state.allCollaborators
  )

  const checkAllCollaborators = useSelectedCollaboratorsTrail(
    (state) => state.checkAllCollaborators
  )

  const allSquads = useSelectedSquadsTrail((state) => state.allSquads)
  const allGroups = useSelectedGroupsTrail((state) => state.allGroups)

  const checkAllSquads = useSelectedSquadsTrail((state) => state.checkAllSquads)
  const checkAllGroups = useSelectedGroupsTrail((state) => state.checkAllGroups)

  const tabActionsCheckedChange: Record<string, () => void> = {
    tab1: checkAllCollaborators,
    tab2: checkAllSquads,
    tab3: checkAllGroups,
  }

  const tabActionsChecked: Record<string, boolean> = {
    tab1: allCollaborators,
    tab2: allSquads,
    tab3: allGroups,
  }

  const onCheckedChange = tabActionsCheckedChange[currentTab]
  const checked = tabActionsChecked[currentTab]
  return (
    <div className="rounded-md border border-solid border-ctx-layout-border p-4">
      <Search
        size="md"
        placeholder={placeholder}
        value={searchValue}
        className="bg-ctx-interactive-secondary [&_button.at-rounded-component-iconButton-border-radius]:hidden"
        handleChange={(e) => setSearchValue(e)}
      />

      <div className="mt-4 flex items-center gap-2">
        <div>
          <Checkbox
            id={title}
            onCheckedChange={onCheckedChange}
            checked={checked}
            size="md"
            className="dark:rounded-sm dark:bg-[#FFFFFFB8]"
          />
        </div>

        <label
          className="cursor-pointer text-ctx-content-base ts-heading-xxs"
          htmlFor={title}
        >
          {title}
        </label>
      </div>
    </div>
  )
}
