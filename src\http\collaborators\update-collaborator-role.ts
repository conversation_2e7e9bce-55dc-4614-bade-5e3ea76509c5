import { UPDATE_EMPLOYEE_ROLE } from '@/graphql/collaborators/mutations'
import {
  UpdateRoleUserMutation,
  UpdateRoleUserMutationVariables,
} from '@/graphql/generated/graphql'
import { clientGraphql } from '@/services/graphql'

export const updateCollaboratorRole = async (
  data: UpdateRoleUserMutationVariables
) =>
  await clientGraphql.request<UpdateRoleUserMutation>(
    UPDATE_EMPLOYEE_ROLE,
    data
  )
