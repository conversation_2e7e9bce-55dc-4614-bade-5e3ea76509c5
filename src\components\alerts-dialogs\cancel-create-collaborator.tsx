import { Button, IconShape } from '@ads/components-react'
import { Info } from 'lucide-react'

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'

type CancelEditTeamAlertProps = {
  disabledCancel?: boolean
  onConfirm?: () => void
  isEditMode?: boolean
}

export function CancelCreateCollaboratorAlert({
  disabledCancel,
  isEditMode,
  onConfirm,
}: CancelEditTeamAlertProps) {
  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button type="button" hierarchy="tertiary" disabled={disabledCancel}>
          Cancelar
        </Button>
      </AlertDialogTrigger>

      <AlertDialogContent className="gap-6">
        <IconShape
          size="sm"
          icon={Info}
          type="danger"
          className="mx-auto sm:mx-0"
        />
        <AlertDialogHeader>
          <AlertDialogTitle className="text-ctx-content-title ts-heading-md">
            {isEditMode
              ? 'Tem certeza que deseja cancelar a edição?'
              : 'Tem certeza que deseja cancelar o cadastro?'}
          </AlertDialogTitle>
          <AlertDialogDescription className="text-ctx-content-base ts-paragraph-xs">
            Ao cancelar, todas as informações preenchidas até agora serão
            perdidas e não poderão ser recuperadas.
          </AlertDialogDescription>
        </AlertDialogHeader>

        <AlertDialogFooter className="gap-y-3">
          <AlertDialogCancel asChild>
            <Button size="lg" hierarchy="secondary" className="w-full sm:w-fit">
              Cancelar
            </Button>
          </AlertDialogCancel>

          <AlertDialogAction asChild>
            {/* timeout to fix a bug where the button is clicked twice */}
            <Button
              onClick={() => {
                setTimeout(() => onConfirm?.(), 100)
              }}
              size="lg"
              hierarchy="danger"
              className="w-full sm:w-fit"
            >
              Sim, confirmo
            </Button>
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
