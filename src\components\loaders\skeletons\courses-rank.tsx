import { Skeleton } from '@/components/ui/skeleton'
import { useIsMobile } from '@/hooks/use-mobile'

export function SkeletonCoursesRank() {
  const isMobile = useIsMobile()

  const bars = isMobile ? 5 : 10

  return (
    <div
      className="h-[300px] w-full sm:h-[500px]"
      style={{
        display: 'grid',
        gridTemplateColumns: `repeat(${bars}, 1fr)`,
        gap: 20,
        alignItems: 'end',
      }}
    >
      {Array.from({ length: bars }).map((_, index) => (
        <Skeleton
          key={index}
          className="animate-pulse rounded-t-lg"
          style={{
            height: `${80 - 5 * index}%`,
          }}
        />
      ))}
    </div>
  )
}
