'use client'

import { Button, IconButton } from '@ads/components-react'
import { Trash2 } from 'lucide-react'

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import { useSelectedUsersGroups } from '@/store/useSelectedUsersGroup'

import { Separator } from '../ui/separator'

interface DeleteUserGroupAlertProps {
  id: number
}

export function DeleteUserGroupAlert({ id }: DeleteUserGroupAlertProps) {
  const { removeSelectedUserId } = useSelectedUsersGroups()

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <IconButton
          hierarchy="tertiary"
          size="md"
          icon={Trash2}
          ariaLabel="Selecionar"
        />
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>
            Tem certeza que deseja excluir esse usuário?
          </AlertDialogTitle>
          <AlertDialogDescription>
            Você pode adicionar novamente o usuário a qualquer momento.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <Separator />
        <AlertDialogFooter>
          <AlertDialogCancel asChild>
            <Button hierarchy="secondary">Voltar</Button>
          </AlertDialogCancel>
          <AlertDialogAction asChild>
            <Button onClick={() => removeSelectedUserId(id)} hierarchy="danger">
              Excluir
            </Button>
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
