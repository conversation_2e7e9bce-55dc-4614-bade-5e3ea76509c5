import { useSelectedCollaboratorsTrail } from '@/store/useSelectedCollaboratorsTrail'
import { useSelectedCoursesTrail } from '@/store/useSelectedCoursesTrail'
import { useSelectedGroupsTrail } from '@/store/useSelectedGroupsTrail'
import { useSelectedSquadsTrail } from '@/store/useSelectedSquadsTrail'
import { useStepOneSaved } from '@/store/useStepOneSaved'

export function clearStates() {
  useStepOneSaved.getState().clearData()
  useSelectedCoursesTrail.getState().setCoursesSelected([])
  useSelectedSquadsTrail.getState().setSquadsSelected([])
  useSelectedGroupsTrail.getState().setGroupsSelected([])
  useSelectedCollaboratorsTrail.getState().setCollaboratorsSelected([])
}
