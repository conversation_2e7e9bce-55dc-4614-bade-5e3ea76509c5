'use client'

import { Checkbox } from '@ads/components-react'
import { useEffect } from 'react'
import Cookies from 'universal-cookie'

import {
  DataTable,
  PaginationInfo,
  TableColumn,
} from '@/components/ui/data-table/data-table'
import { GetTeamsQuery } from '@/graphql/generated/graphql'
import { SquadsData } from '@/model/study-plan'
import { useSelectedSquadsTrail } from '@/store/useSelectedSquadsTrail'

type StudyPlanTableProps = {
  squads: GetTeamsQuery['companySquads']
  isLoading: boolean
  currentPage: number
  setCurrentPage: (page: number) => void
  isFetchingNewPage?: boolean
}

export function SquadsTable({
  squads,
  isLoading,
  currentPage,
  setCurrentPage,
  isFetchingNewPage = false,
}: StudyPlanTableProps) {
  const paginationInfo: PaginationInfo = {
    currentPage,
    totalPages: Math.ceil(squads.total / squads.perPage) || 1,
    itemsPerPage: squads.perPage,
    totalItems: squads.total,
  }

  const toggleSquad = useSelectedSquadsTrail((state) => state.toggleSquad)
  const setSquadsSelected = useSelectedSquadsTrail(
    (state) => state.setSquadsSelected
  )

  const isSquadSelecionado = useSelectedSquadsTrail(
    (state) => state.isSquadSelecionado
  )

  const cookies = new Cookies(null, { path: '/' })

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  useEffect(() => {
    const savedStep3 = cookies.get('step3')
    if (savedStep3 && typeof savedStep3 === 'object' && savedStep3.squads) {
      const { squads } = savedStep3

      setSquadsSelected(squads.selected ?? [])

      useSelectedSquadsTrail.setState({
        allSquads: squads.all ?? false,
        excludedSquads: squads.excluded ?? [],
      })
    }
  }, [])

  const columns: TableColumn<SquadsData>[] = [
    {
      key: 'status',
      header: 'Selecionar',
      accessor: (squad) => {
        const squadId = Number(squad.id)
        const squadName = String(squad.title)

        const isChecked = isSquadSelecionado(squadId)

        return (
          <div className="flex justify-center pr-6">
            <Checkbox
              onCheckedChange={() =>
                toggleSquad({
                  id: squadId,
                  name: squadName,
                })
              }
              checked={isChecked}
              size="md"
              className="dark:rounded-sm dark:bg-[#FFFFFFB8]"
            />
          </div>
        )
      },
      headerClassName: 'text-center w-10',
      className: 'text-center',
    },

    {
      key: 'title',
      header: 'Nome da Equipe',
      accessor: 'title',
    },
  ]

  return (
    <DataTable
      data={squads.data as SquadsData[]}
      columns={columns}
      pagination={paginationInfo}
      onPageChange={handlePageChange}
      loading={isLoading}
      isFetchingNewPage={isFetchingNewPage}
      emptyMessage="Nenhuma equipe encontrada."
      className="overflow-hidden p-0"
    />
  )
}
