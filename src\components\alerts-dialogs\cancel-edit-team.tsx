import { Button, IconShape } from '@ads/components-react'
import { Info } from 'lucide-react'

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'

type CancelEditTeamAlertProps = {
  disabledCancel?: boolean
  showAlert?: boolean
  onClickCancel?: () => void
}

export function CancelEditTeamAlert({
  disabledCancel,
  showAlert = true,
  onClickCancel,
}: CancelEditTeamAlertProps) {
  if (!showAlert) {
    return (
      <Button
        size="md"
        hierarchy="tertiary"
        disabled={disabledCancel}
        onClick={onClickCancel}
      >
        Cancelar
      </Button>
    )
  }

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button disabled={disabledCancel} hierarchy="tertiary" size="md">
          Cancelar
        </Button>
      </AlertDialogTrigger>

      <AlertDialogContent className="gap-6">
        <IconShape
          size="sm"
          icon={Info}
          type="danger"
          className="mx-auto sm:mx-0"
        />
        <AlertDialogHeader>
          <AlertDialogTitle className="text-ctx-content-title ts-heading-md">
            Tem certeza que deseja cancelar?
          </AlertDialogTitle>
          <AlertDialogDescription className="text-ctx-content-base ts-paragraph-xs">
            Ao cancelar, todas as informações preenchidas até agora serão
            perdidas e não poderão ser recuperadas.
          </AlertDialogDescription>
        </AlertDialogHeader>

        <AlertDialogFooter className="gap-y-3">
          <AlertDialogCancel asChild>
            <Button size="lg" hierarchy="secondary" className="w-full sm:w-fit">
              Cancelar
            </Button>
          </AlertDialogCancel>

          <AlertDialogAction asChild>
            <Button
              onClick={onClickCancel}
              size="lg"
              hierarchy="danger"
              className="w-full sm:w-fit"
            >
              Cancelar edição
            </Button>
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
