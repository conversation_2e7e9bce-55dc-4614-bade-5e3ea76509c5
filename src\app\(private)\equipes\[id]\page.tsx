'use client'

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, useAlert } from '@ads/components-react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { useEffect, useMemo, useState } from 'react'
import { useMutation, useQuery } from 'react-query'
import Cookies from 'universal-cookie'

import { CancelEditTeamAlert } from '@/components/alerts-dialogs/cancel-edit-team'
import { PageLayout } from '@/components/layouts/page-layout'
import { TeamsSelectedTable } from '@/components/tables/teams/users-selected'
import { Skeleton } from '@/components/ui/skeleton'
import { AddUsersModal } from '@/components/ui/teams/modal'
import { getTeamById } from '@/http/teams/get-team-by-id'
import { updateTeam } from '@/http/teams/update-team'
import { getB2bUsers } from '@/http/user'
import { queryClient } from '@/lib/queryClient'
import { useSelectedUsers } from '@/store/useSelectedUsersTeams'

export default function EquipesId() {
  const router = useRouter()
  const params = useParams()

  const { alert } = useAlert()
  const { selectedUserIds, setSelected, clearSelected } = useSelectedUsers()

  const [teamName, setTeamName] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [error, setError] = useState(false)

  const { companyId, enrollmentId } = useMemo(() => {
    const cookies = new Cookies(null, { path: '/' })
    return {
      companyId: cookies.get('b2bCompanyId'),
      enrollmentId: Number(cookies.get('b2bEnrollmentId')),
    }
  }, [])

  const teamId = params.id
  const perPage = 10

  const {
    data: team,
    isLoading: isLoadingTeam,
    isFetching: isFetchingTeam,
  } = useQuery({
    queryKey: ['getTeamByIdGql', teamId],
    staleTime: 0,
    cacheTime: 0,
    queryFn: () =>
      getTeamById({
        id: Number(teamId),
        page: 1,
        limit: 9999,
      }),
    onError: () => {
      alert({
        title: 'Equipe não encontrada!',
        description: `Não foi possível localizar está equipe. Tente novamente.`,
        alertType: 'danger',
      })
      handleBackPage()
    },
  })

  const {
    data: b2bUsers,
    isLoading: isLoadingUsers,
    isFetching: isFetchingUsers,
  } = useQuery({
    queryKey: ['getb2bUsersSelectedGql'],
    keepPreviousData: true,
    staleTime: 1000 * 60 * 10, // 10 minutes
    queryFn: () =>
      getB2bUsers({
        page: 1,
        limit: 9999,
        orderBy: 'NAME',
        company_id: companyId,
        enrollment_id: enrollmentId,
        hasSquad: false,
      }),
    onError: () => {
      alert({
        title: 'Colaboradores não encontrados!',
        description: `Não foi possível localizar os colaboradores desta equipe. Tente novamente.`,
        alertType: 'danger',
      })
      handleBackPage()
    },
  })

  const teamUsers = team?.companySquad?.users?.data ?? []

  const allSelectableUsers = useMemo(() => {
    const uniqueUsersMap = new Map()

    b2bUsers?.users.data.forEach((user) => {
      uniqueUsersMap.set(user.id, user)
    })

    teamUsers.forEach((user) => {
      if (!uniqueUsersMap.has(user.id)) {
        uniqueUsersMap.set(user.id, user)
      }
    })

    return Array.from(uniqueUsersMap.values())
  }, [b2bUsers, teamUsers])

  const filteredUsers = useMemo(() => {
    return allSelectableUsers.filter((user) =>
      selectedUserIds.includes(user.id)
    )
  }, [allSelectableUsers, selectedUserIds])

  const paginatedUsers = useMemo(
    () =>
      filteredUsers.slice((currentPage - 1) * perPage, currentPage * perPage),
    [filteredUsers, currentPage, perPage]
  )

  const usersByCompanySelected = useMemo(
    () => ({
      data: paginatedUsers,
      total: filteredUsers.length,
      perPage,
    }),
    [paginatedUsers, filteredUsers.length, perPage]
  )

  const { mutate: onUpdateTeam, isLoading: isLoadingUpdate } = useMutation({
    mutationFn: updateTeam,
    onSuccess: () => {
      queryClient.refetchQueries(['getTeamsGql'], {
        active: true,
        exact: false,
      })
      alert({
        title: 'Equipe atualiza com sucesso!',
        description: `A equipe ${teamName} foi atualizada`,
        alertType: 'success',
      })
      handleBackPage()
    },
    onError: () => {
      alert({
        title: 'Erro ao atualizar equipe!',
        description: `Não foi possível atualizar está equipe. Tente novamente.`,
        alertType: 'danger',
      })
    },
  })

  const isTeamDataUnchanged =
    selectedUserIds.length === teamUsers.length &&
    teamUsers.every((user) => selectedUserIds.includes(user.id)) &&
    team?.companySquad.title === teamName

  const handleUpdateTeams = () => {
    if (!teamName) {
      alert({
        alertType: 'danger',
        title: 'Equipe sem nome',
        description: 'Por favor, informe o nome da equipe.',
      })
      setError(true)
      return
    }

    setError(false)

    onUpdateTeam({
      id: Number(teamId),
      company_id: companyId,
      title: teamName,
      user_ids: selectedUserIds,
    })
  }

  const handleBackPage = () => {
    clearSelected()
    router.push('/equipes')
  }

  useEffect(() => {
    if (!team) return

    clearSelected()

    setTeamName(team.companySquad?.title)

    const userIds = team.companySquad?.users?.data.map(({ id }) => id) || []
    setSelected(userIds)
  }, [team?.companySquad])

  return (
    <PageLayout
      title="Equipes"
      description="Gerencie, credencie e edite as equipes."
    >
      <div className="rounded-2xl bg-ctx-layout-body p-4 md:p-8">
        <div className="flex flex-col items-end justify-between gap-8 rounded-2xl border border-solid border-ctx-layout-border p-4 md:flex-row">
          {isLoadingTeam ? (
            <Skeleton className="mt-6 h-10 w-full md:w-4/5" />
          ) : (
            <>
              <TextField
                label="Nome"
                value={teamName}
                onChange={(e) => setTeamName(e.target.value)}
                hasError={error}
                size="md"
                placeholder="Digite o nome da Equipe"
                fullWidth
                custom={{
                  input: 'bg-ctx-interactive-secondary',
                }}
              />

              <AddUsersModal />
            </>
          )}
        </div>

        <div className="mt-6 w-full">
          <TeamsSelectedTable
            isLoading={isLoadingTeam || isLoadingUsers}
            isFetchingNewPage={isFetchingTeam || isFetchingUsers}
            currentPage={currentPage}
            setCurrentPage={setCurrentPage}
            usersByCompany={usersByCompanySelected}
          />
        </div>
      </div>
      <div className="flex justify-end gap-8 py-6">
        <CancelEditTeamAlert
          onClickCancel={handleBackPage}
          disabledCancel={isLoadingUpdate}
          showAlert={!isTeamDataUnchanged}
        />
        <Button
          isLoading={isLoadingUpdate}
          disabled={isLoadingUpdate || isTeamDataUnchanged}
          onClick={handleUpdateTeams}
          size="md"
        >
          Salvar
        </Button>
      </div>
    </PageLayout>
  )
}
