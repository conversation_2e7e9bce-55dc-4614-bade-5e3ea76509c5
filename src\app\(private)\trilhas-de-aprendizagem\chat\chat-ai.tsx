'use client'

import { But<PERSON>, Checkbox, Search, useAlert } from '@ads/components-react'
import { ArrowDownIcon } from 'lucide-react'
import { useEffect, useRef, useState } from 'react'
import {
  useInfiniteQuery,
  useMutation,
  useQuery,
  useQueryClient,
} from 'react-query'
import Cookies from 'universal-cookie'

import NotFound from '@/app/not-found'
import { CourseCard } from '@/components/course-card'
import { InputContextMessage } from '@/components/Input-context-message'
import { ChatPageSkeleton } from '@/components/loaders/skeletons/chat-page'
import { TypingIndicator } from '@/components/loaders/typing-indicator'
import { ResponseAI } from '@/components/response-ai'
import { InputChat } from '@/components/ui/input-chat'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import { Skeleton } from '@/components/ui/skeleton'
import { useAuth } from '@/contexts/AuthContext'
import {
  CreateStudyPlanMutationVariables,
  type UpdatedTeamsAndUserPlanStudyMutationVariables,
} from '@/graphql/generated/graphql'
import { useDebouncedValue } from '@/hooks/use-debounce'
import { getChatMessageAI } from '@/http/chat-ai/get-chat-ai-message'
import { patchMessageAi } from '@/http/chat-ai/patch-message-ai'
import {
  createNewStudyPlan,
  updateTeamsUsersStudyPlan,
} from '@/http/study-plan'
import { getB2bUsers } from '@/http/user'
import { useChatStore } from '@/store/useChatStore'

export interface Course {
  id: number
  title: string
  justification: string
  description: string
  image_url: string
}

interface ChatMessage {
  id: string
  type: 'user' | 'ai'
  content: string
  timestamp: Date
  metadata?: {
    courses?: Course[]
    selectedCourses?: string[]
    studyPlanCreated?: boolean
    showCoursesSelection?: boolean
    isNamingStep?: boolean
    showAssignButton?: boolean
  }
}

export function ChatAI() {
  const { alert } = useAlert()
  const { user } = useAuth()
  const { chatId } = useChatStore()
  const queryClient = useQueryClient()
  const cookies = new Cookies(null, { path: '/' })
  const companyId = cookies.get('b2bCompanyId')
  const enrollmentId = cookies.get('b2bEnrollmentId')

  const [selectedIdsByMessageId, setSelectedIdsByMessageId] = useState<
    Record<string, string[]>
  >({})
  const [showInputStudyPlanName, setShowInputStudyPlanName] = useState(false)
  const [studyPlanName, setStudyPlanName] = useState('')
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([])
  const [assignClicked, setAssignClicked] = useState(false)
  const [selectedUserIds, setSelectedUserIds] = useState<number[]>([])
  const [studyPlanId, setStudyPlanId] = useState<null | number>(null)
  const [newMessage, setNewMessage] = useState('')
  const [searchUserValue, setSearchUserValue] = useState<string>('')
  const [visibleCoursesByMessageId, setVisibleCoursesByMessageId] = useState<
    Record<string, number>
  >({})

  const messagesEndRef = useRef<HTMLDivElement>(null)

  const {
    data: dataGetMessageAI,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ['chat', chatId],
    queryFn: () => getChatMessageAI(chatId as string),
    onSuccess: (data) => {
      if (data && data.length > 0) {
        const messages: ChatMessage[] = data.flatMap((item, index) => [
          {
            id: `user-${Date.now() + index}`,
            type: 'user',
            content: item?.user_input ?? '',
            timestamp: new Date(),
          },
          {
            id: `ai-${Date.now() + index}`,
            type: 'ai',
            content: item.ai_response.content || item.ai_response.description,
            timestamp: new Date(),
            metadata: {
              courses: item.ai_response.courses,
              showCoursesSelection: item.ai_response.courses?.length > 0,
            },
          },
        ])

        // Verificar se já temos mensagens no estado antes de sobrescrever
        setChatMessages((prevMessages) => {
          // Se já temos mensagens e o chatId é o mesmo, mantemos as mensagens existentes
          if (prevMessages.length > 0) {
            return prevMessages
          }
          // Caso contrário, usamos as mensagens da API
          return messages
        })
      }
    },
  })

  const { mutate: sendMessage, isLoading: isSendingMessage } = useMutation({
    mutationFn: (context: string) => patchMessageAi(chatId as string, context),
    onSuccess: () => {
      // Não invalidamos a consulta para evitar a perda do histórico
      // A mensagem do usuário já foi adicionada ao histórico na função handleSendMessage
      // O refetch será chamado apenas para obter a resposta da IA

      // Chamamos refetch para obter a resposta da IA
      setTimeout(async () => {
        const response = await refetch()

        // Se a resposta contiver dados, adicionamos a resposta da IA ao histórico
        if (response.data && response.data.length > 0) {
          const lastItem = response.data[response.data.length - 1]

          addMessageToHistory({
            type: 'ai',
            content:
              lastItem.ai_response.content || lastItem.ai_response.description,
            metadata: {
              courses: lastItem.ai_response.courses,
              showCoursesSelection: lastItem.ai_response.courses?.length > 0,
            },
          })
        }

        if (messagesEndRef.current) {
          const scrollContainer = messagesEndRef.current.querySelector(
            '[data-radix-scroll-area-viewport]'
          )
          if (scrollContainer) {
            scrollContainer.scrollTop = scrollContainer.scrollHeight
          }
        }
      }, 100)
    },
    onError: () => {
      alert({
        title: 'Erro ao enviar mensagem',
        description:
          'Ocorreu um erro ao enviar sua mensagem. Tente novamente mais tarde.',
        alertType: 'danger',
      })
    },
  })

  const {
    mutate: updatedStudyPlanMutation,
    isLoading: isUpdatingStudyPlan,
    isSuccess: isUpdatingStudyPlanSuccess,
  } = useMutation({
    mutationFn: (data: UpdatedTeamsAndUserPlanStudyMutationVariables) =>
      updateTeamsUsersStudyPlan(data),
    onSuccess: () => {
      queryClient.invalidateQueries(['getStudyPlansGql'])

      addMessageToHistory({
        type: 'user',
        content: 'Confirmar atribuição',
      })

      addMessageToHistory({
        type: 'ai',
        content: `<span>Usuários atribuídos com sucesso!</span>`,
        metadata: { studyPlanCreated: true },
      })

      setAssignClicked(false)

      setShowInputStudyPlanName(false)
    },
    onError: () => {
      addMessageToHistory({
        type: 'ai',
        content: `<span>Ocorreu um erro ao atribuir os usuários à trilha </span>`,
        metadata: { studyPlanCreated: true },
      })

      setAssignClicked(false)
    },
  })

  const { mutate: createStudyPlanMutation, isLoading: isCreatingStudyPlan } =
    useMutation({
      mutationFn: (data: CreateStudyPlanMutationVariables) =>
        createNewStudyPlan(data),
      onSuccess: (response, data) => {
        queryClient.invalidateQueries({
          predicate: (query) => {
            return query.queryKey[0] === 'getStudyPlansGql'
          },
        })

        addMessageToHistory({
          type: 'ai',
          content: `<span>Trilha de aprendizado criada com sucesso!</span><br/><br/>A trilha "${data.name}" foi criada com sucesso com ${Array.isArray(data.courses) ? data.courses.length : 1} curso(s) selecionado(s).  Para quem você gostaria de atribuir a trilha?`,
          metadata: { studyPlanCreated: true },
        })

        setStudyPlanId(response.createStudyPlan.id)
      },
      onError: () => {
        addMessageToHistory({
          type: 'ai',
          content: 'Ocorreu um erro ao criar a trilha. Vamos tentar novamente?',
        })
      },
    })

  const debouncedSearchUserValue = useDebouncedValue(searchUserValue, 500)

  const {
    data: users,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isFetching,
    refetch: refetchUsers,
  } = useInfiniteQuery({
    queryKey: ['getb2bUsersGql', debouncedSearchUserValue],
    queryFn: ({ pageParam = 1 }) =>
      getB2bUsers({
        page: pageParam,
        limit: 10,
        orderBy: 'NAME',
        company_id: companyId,
        enrollment_id: enrollmentId,
        q: debouncedSearchUserValue || undefined,
      }),

    getNextPageParam: (response, allPagesData) => {
      const nextPage = allPagesData.length + 1
      return nextPage <= Math.ceil(response.users.total / 10)
        ? nextPage
        : undefined
    },
    keepPreviousData: false,
    staleTime: 60 * 1000 * 10, // 10 minutos
    cacheTime: 0,
  })

  const flattedData = users?.pages?.flatMap((page) => page.users.data) ?? []

  // Resetar para a primeira página quando o termo de busca mudar
  useEffect(() => {
    if (debouncedSearchUserValue !== undefined) {
      refetchUsers()
    }
  }, [debouncedSearchUserValue, refetchUsers])

  function loadMore() {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage()
    }
  }

  function addMessageToHistory(message: Omit<ChatMessage, 'id' | 'timestamp'>) {
    const newMessage: ChatMessage = {
      ...message,
      id: `${message.type}-${Date.now()}`,
      timestamp: new Date(),
    }
    setChatMessages((prev) => [...prev, newMessage])
  }

  function handleCardChange(id: string, checked: boolean, messageId: string) {
    setSelectedIdsByMessageId((prev) => {
      const currentMessageIds = prev[messageId] || []
      const set = new Set(currentMessageIds)
      if (checked) set.add(id)
      else set.delete(id)
      return {
        ...prev,
        [messageId]: Array.from(set),
      }
    })
  }

  function handleUserCheckChange(userId: number, checked: boolean) {
    setSelectedUserIds((prev) => {
      const set = new Set(prev)
      if (checked) set.add(userId)
      else set.delete(userId)
      return Array.from(set)
    })
  }

  function handleStartStudyPlanCreation(messageId: string) {
    setShowInputStudyPlanName(true)

    // Obter os cursos selecionados para este bloco específico
    const selectedCoursesForThisMessage =
      selectedIdsByMessageId[messageId] || []

    addMessageToHistory({
      type: 'user',
      content: 'Criar trilha com os cursos selecionados',
    })

    addMessageToHistory({
      type: 'ai',
      content: 'Vamos criar uma trilha nova! Como você gostaria de chamá-la?',
      metadata: {
        isNamingStep: true,
        selectedCourses: selectedCoursesForThisMessage,
      },
    })
  }

  function handleAssignCollaborators() {
    setAssignClicked(true)
  }

  function handleStudyPlanNameSubmit(name: string) {
    // Obter os cursos selecionados da última mensagem com isNamingStep: true
    const namingStepMessage = [...chatMessages]
      .reverse()
      .find((msg) => msg.metadata?.isNamingStep)
    const selectedCoursesForCreation =
      namingStepMessage?.metadata?.selectedCourses || []

    addMessageToHistory({
      type: 'user',
      content: `Nome da trilha: "${name}"`,
    })

    createStudyPlanMutation({
      name,
      description: '',
      courses: selectedCoursesForCreation.map((id, index) => ({
        course_id: Number(id),
        order: index,
      })),
      company_id: user?.metadata.company_id as number,
      squad_ids: [],
      user_ids: [],
      is_pdi: false,
      is_for_all_users: false,
      is_for_all_squads: false,
      is_for_all_courses: false,
    })

    setShowInputStudyPlanName(false)
  }

  function handleStudyPlanAssignment() {
    setShowInputStudyPlanName(true)
    if (selectedUserIds) {
      updatedStudyPlanMutation({
        id: studyPlanId as number,
        squad_ids: [],
        user_ids: selectedUserIds,
        notify: true,
        is_for_all_users: false,
        is_for_all_squads: false,
        is_for_all_courses: false,
      })
    }
  }

  function handleSendMessage() {
    if (!newMessage.trim() || isSendingMessage) return

    const messageToSend = newMessage.trim()

    addMessageToHistory({
      type: 'user',
      content: messageToSend,
    })

    setNewMessage('')

    sendMessage(messageToSend)

    setTimeout(() => {
      scrollToBottom()
    }, 50)
  }

  function handleSendMessageKeyDown(
    e: React.KeyboardEvent<HTMLTextAreaElement>
  ) {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  function handleSendKeyDown(
    e: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>
  ) {
    if (e.key === 'Enter') {
      e.preventDefault()
      const value = (e.target as HTMLTextAreaElement).value
      if (value) {
        handleStudyPlanNameSubmit(value)
      }
    }
  }

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [chatMessages])

  if (isLoading) return <ChatPageSkeleton />
  if (!dataGetMessageAI) return <NotFound />

  const lastAiMessage = [...chatMessages]
    .reverse()
    .find((msg) => msg.type === 'ai')

  const hasCourses =
    lastAiMessage?.metadata?.courses &&
    lastAiMessage.metadata.courses.length > 0

  const studyPlanWasCreated = chatMessages.some(
    (msg) => msg.metadata?.studyPlanCreated
  )

  return (
    <div className="flex h-full flex-col overflow-hidden bg-ctx-layout-body md:h-[calc(100vh)]">
      <div className="min-h-0 flex-1 overflow-hidden">
        <ScrollArea className="h-full">
          <div className="mx-auto w-full max-w-[58rem] space-y-4 p-4 md:px-4">
            {chatMessages.map((message) => (
              <div key={message.id}>
                {message.type === 'user' ? (
                  <InputContextMessage content={message.content} />
                ) : (
                  <ResponseAI content={message.content} />
                )}

                {message.type === 'ai' &&
                  message.metadata?.courses &&
                  message.metadata?.showCoursesSelection && (
                    <>
                      <div className="mt-4 space-y-4">
                        <div className="@container">
                          <div className="grid grid-cols-1 gap-4 @[640px]:grid-cols-2 @[1200px]:grid-cols-4">
                            {message.metadata.courses
                              ?.slice(
                                0,
                                visibleCoursesByMessageId[message.id] || 4
                              )
                              .map((course: Course, courseIndex: number) => (
                                <div key={course.id}>
                                  <CourseCard
                                    id={String(course.id)}
                                    index={courseIndex + 1}
                                    title={course.title}
                                    imageUrl={course.image_url}
                                    imageAlt={`Imagem do curso ${course.title}`}
                                    justification={course.justification}
                                    messageId={message.id}
                                    checked={(
                                      selectedIdsByMessageId[message.id] || []
                                    ).includes(String(course.id))}
                                    onChange={(id, checked) =>
                                      handleCardChange(id, checked, message.id)
                                    }
                                  />
                                </div>
                              ))}
                          </div>
                        </div>

                        {/* Botão para carregar mais */}
                        {message.metadata.courses.length >
                          (visibleCoursesByMessageId[message.id] || 4) && (
                          <div
                            className="flex justify-center pt-2"
                            data-message-id={message.id}
                          >
                            <Button
                              hierarchy="secondary"
                              trailingIcon={ArrowDownIcon}
                              onClick={() => {
                                const currentVisible =
                                  visibleCoursesByMessageId[message.id] || 4
                                const newVisible = currentVisible + 4
                                const isLastBatch =
                                  newVisible >=
                                  (message.metadata?.courses?.length ?? 0)

                                setVisibleCoursesByMessageId((prev) => ({
                                  ...prev,
                                  [message.id]: newVisible,
                                }))

                                if (isLastBatch) {
                                  setTimeout(() => {
                                    if (messagesEndRef.current) {
                                      messagesEndRef.current.scrollIntoView({
                                        behavior: 'smooth',
                                      })
                                    }
                                  }, 300)
                                }
                              }}
                            >
                              Carregar mais sugestões
                            </Button>
                          </div>
                        )}
                      </div>

                      {!studyPlanWasCreated && hasCourses && (
                        <div className="mt-4 flex flex-col gap-4">
                          <span className="text-ctx-content-base ts-paragraph-xs">
                            Se você quiser posso criar uma trilha com base nos
                            curso(s) que você selecionar acima, ou continue
                            pesquisando por mais cursos:
                          </span>

                          {(selectedIdsByMessageId[message.id] || []).length >
                            0 && (
                            <Button
                              onClick={() =>
                                handleStartStudyPlanCreation(message.id)
                              }
                              hierarchy="primary"
                              disabled={
                                (selectedIdsByMessageId[message.id] || [])
                                  .length === 0 || isCreatingStudyPlan
                              }
                            >
                              Criar trilha com os cursos selecionados
                            </Button>
                          )}

                          <div ref={messagesEndRef} />
                        </div>
                      )}
                    </>
                  )}

                {message.metadata?.studyPlanCreated && (
                  <div className="mt-4 space-y-4">
                    {assignClicked && (
                      <>
                        <InputContextMessage content="Atribuir a Colaborador(es)" />
                        <div className="mt-2 space-y-2">
                          <ResponseAI
                            content={`
                              <span>
                                Encontrei ${users?.pages?.[0].users.total} Colaboradores. Selecione os que deseja incluir na trilha:
                              </span>`}
                          />

                          {/* Campo de busca para filtrar usuários */}
                          <div className="w-full [&_button.at-rounded-component-iconButton-border-radius]:hidden">
                            <Search
                              size="md"
                              placeholder="Buscar colaborador por nome ou e-mail"
                              className="bg-ctx-interactive-secondary [&_button.at-rounded-component-iconButton-border-radius]:hidden"
                              handleChange={(e) => {
                                setSearchUserValue(e)
                              }}
                              value={searchUserValue}
                              query={searchUserValue ?? ''}
                            />
                          </div>

                          <div className="space-y-4 rounded-2xl border border-ctx-layout-border p-4">
                            {(debouncedSearchUserValue !== undefined &&
                              isFetching) ||
                            isFetchingNextPage ? (
                              <div className="space-y-4">
                                {Array.from({ length: 5 }).map((_, index) => (
                                  <div
                                    key={index}
                                    className="flex items-center space-x-2"
                                  >
                                    <Skeleton className="h-5 w-5 rounded-sm" />
                                    <Skeleton className="h-5 w-full rounded-md" />
                                  </div>
                                ))}
                              </div>
                            ) : flattedData.length > 0 ? (
                              flattedData.map((user) => (
                                <div
                                  key={user.id}
                                  className="flex items-center space-x-2"
                                >
                                  <Checkbox
                                    checked={selectedUserIds.includes(user.id)}
                                    onCheckedChange={(checked) =>
                                      handleUserCheckChange(
                                        user.id,
                                        Boolean(checked)
                                      )
                                    }
                                    disabled={false}
                                    size="md"
                                    className="dark:rounded-sm dark:bg-[#FFFFFFB8]"
                                  />

                                  <div>
                                    <span className="text-ctx-content-title ts-paragraph-xs">
                                      {user.name} ({user.email})
                                    </span>
                                  </div>
                                </div>
                              ))
                            ) : (
                              <div className="flex flex-col items-center justify-center py-4">
                                <span className="text-ctx-content-title ts-paragraph-xs">
                                  Usuário não encontrado
                                </span>
                              </div>
                            )}

                            {hasNextPage && (
                              <div className="flex flex-col items-center">
                                <Button
                                  hierarchy="secondary"
                                  trailingIcon={ArrowDownIcon}
                                  onClick={loadMore}
                                >
                                  {isFetchingNextPage
                                    ? 'Carregando colaboradores...'
                                    : 'Carregar mais colaboradores'}
                                </Button>
                              </div>
                            )}
                          </div>

                          <Button
                            onClick={handleStudyPlanAssignment}
                            hierarchy="primary"
                            disabled={
                              selectedUserIds.length === 0 ||
                              isUpdatingStudyPlan ||
                              isUpdatingStudyPlanSuccess
                            }
                          >
                            Confirmar atribuição
                          </Button>

                          <div ref={messagesEndRef} />
                        </div>
                      </>
                    )}

                    {!assignClicked && !isUpdatingStudyPlanSuccess && (
                      <>
                        <Button
                          onClick={handleAssignCollaborators}
                          hierarchy="primary"
                          className={
                            assignClicked
                              ? 'border-2 border-ctx-highlight-focus'
                              : ''
                          }
                        >
                          Atribuir a Colaborador(es)
                        </Button>
                        <div ref={messagesEndRef} />
                      </>
                    )}
                  </div>
                )}
              </div>
            ))}
            {isSendingMessage && <TypingIndicator />}
          </div>
        </ScrollArea>
      </div>

      <div className="flex-shrink-0">
        <Separator className="my-2 md:my-4" />
        <div className="mx-auto w-full max-w-[58rem] px-2 pb-4 md:px-4">
          {showInputStudyPlanName && (
            <InputChat
              placeholder="Digite o nome da trilha e pressione Enter..."
              onChange={(e) => setStudyPlanName(e.target.value)}
              onKeyDown={(e) => {
                handleSendKeyDown(e)
              }}
              onClickSendMessage={() => {
                handleStudyPlanNameSubmit(studyPlanName)
              }}
              disabled={isCreatingStudyPlan}
            />
          )}

          {!showInputStudyPlanName && (
            <InputChat
              placeholder="Descreva seu desafio, dúvida ou envie um arquivo."
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              disabled={
                isCreatingStudyPlan ||
                Object.values(selectedIdsByMessageId).some(
                  (ids) => ids.length > 0
                ) ||
                assignClicked ||
                isSendingMessage
              }
              onClickSendMessage={handleSendMessage}
              onKeyDown={handleSendMessageKeyDown}
              disabledButtonMessage={isSendingMessage || !newMessage.trim()}
            />
          )}
        </div>
      </div>
    </div>
  )
}
