import { Checkbox } from '@ads/components-react'
import { Loader2 } from 'lucide-react'
import * as React from 'react'

import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { LabelAndValue } from '@/model/labelAndValue'

import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from './command'
import { InputField } from './input-form'

type MultiselectComboboxProps = {
  label?: string
  placeholder?: string
  searchPlaceholder?: string
  emptyMessage?: string
  options: LabelAndValue[]
  value?: LabelAndValue[]
  onChange: (value: LabelAndValue[]) => void
  errorMessage?: string
  disabled?: boolean
  isLoading?: boolean
  maxSelectedDisplay?: number
  className?: string
  selectAllLabel?: string
  clearAllLabel?: string
  showSelectAll?: boolean
  showClearAll?: boolean
  handleSearchInput: (value: string) => void
  isFetchingNextPage?: boolean
  hasNextPage?: boolean
  fetchNextPage?: () => void
}

export function AsyncCombobox({
  label,
  placeholder = 'Selecione opções...',
  searchPlaceholder = 'Pesquisar...',
  emptyMessage = 'Não encontrado.',
  options,
  value = [],
  onChange,
  errorMessage,
  disabled = false,
  maxSelectedDisplay = 3,
  selectAllLabel = 'Selecionar todas',
  showSelectAll = true,
  showClearAll = true,
  handleSearchInput,
  isLoading = false,
  isFetchingNextPage = false,
  hasNextPage = false,
  fetchNextPage = () => {},
}: MultiselectComboboxProps) {
  const scrollAreaRef = React.useRef<HTMLDivElement>(null)
  const [open, setOpen] = React.useState(false)
  const [searchValue, setSearchValue] = React.useState('')

  const handleSearch = (value: string) => {
    handleSearchInput(value)
    setSearchValue(value)
  }

  const selectedValues = React.useMemo(
    () => new Set(value.map((item) => item.value)),
    [value]
  )

  const filteredOptions = React.useMemo(() => {
    if (!searchValue) return options
    return options.filter((option) =>
      option.label.toLowerCase().includes(searchValue.toLowerCase())
    )
  }, [options, searchValue])

  const isAllSelected = React.useMemo(() => {
    return (
      filteredOptions.length > 0 &&
      filteredOptions.every((option) => selectedValues.has(option.value))
    )
  }, [filteredOptions, selectedValues])

  const handleSelect = React.useCallback(
    (option: LabelAndValue) => {
      const newValue = selectedValues.has(option.value)
        ? value.filter((item) => item.value !== option.value)
        : [...value, option]
      onChange(newValue)
    },
    [value, selectedValues, onChange]
  )

  const handleSelectAll = React.useCallback(() => {
    if (isAllSelected) {
      const filteredValues = new Set(filteredOptions.map((opt) => opt.value))
      const newValue = value.filter((item) => !filteredValues.has(item.value))
      onChange(newValue)
    } else {
      const newItems = filteredOptions.filter(
        (option) => !selectedValues.has(option.value)
      )
      onChange([...value, ...newItems])
    }
  }, [isAllSelected, filteredOptions, value, selectedValues, onChange])

  const displayText = React.useMemo(() => {
    if (value.length === 0) return placeholder
    if (value.length <= maxSelectedDisplay) {
      return value.map((item) => item.label).join(', ')
    }
    return `${value
      .slice(0, maxSelectedDisplay)
      .map((item) => item.label)
      .join(', ')} +${value.length - maxSelectedDisplay} mais`
  }, [value, maxSelectedDisplay, placeholder])

  function loadMore() {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage()
    }
  }

  React.useEffect(() => {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          loadMore()
        }
      })
    })

    if (scrollAreaRef.current) {
      observer.observe(scrollAreaRef.current)
    }

    return () => {
      observer.disconnect()
    }
  }, [])

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger disabled={disabled} className="text-start">
        <InputField
          label={label}
          value={displayText}
          errorMessage={errorMessage}
          disabled={disabled}
          placeholder={placeholder}
          custom={{
            input:
              'h-10 w-full cursor-pointer truncate text-start text-ctx-content-base',
          }}
        />
      </PopoverTrigger>

      <PopoverContent
        className="w-[--radix-popover-trigger-width] rounded-lg bg-ctx-layout-spotlight p-1"
        align="start"
        asChild
      >
        <Command>
          <CommandInput
            className="outline-none"
            placeholder={searchPlaceholder}
            value={searchValue}
            onValueChange={handleSearch}
          />
          <CommandList>
            {isLoading && filteredOptions.length === 0 ? (
              <div className="flex items-center justify-center">
                <Loader2 className="h-4 w-4 animate-spin" />
              </div>
            ) : (
              <CommandEmpty>{emptyMessage}</CommandEmpty>
            )}
            <CommandGroup>
              {(showSelectAll || showClearAll) && (
                <>
                  {showSelectAll && filteredOptions.length > 0 && (
                    <CommandItem
                      onSelect={handleSelectAll}
                      className="text-ctx-content-title ts-paragraph-xxs"
                    >
                      <Checkbox
                        checked={isAllSelected}
                        custom={{ item: 'bg-ctx-interactive-secondary' }}
                      />
                      {selectAllLabel}
                    </CommandItem>
                  )}
                </>
              )}

              {filteredOptions.map((option) => {
                const isSelected = selectedValues.has(option.value)
                return (
                  <CommandItem
                    key={option.value}
                    value={option.label}
                    onSelect={() => handleSelect(option)}
                    className="text-ctx-content-title ts-paragraph-xxs"
                  >
                    <Checkbox
                      checked={isSelected}
                      custom={{ item: 'bg-ctx-interactive-secondary' }}
                      label={option.label}
                    />
                  </CommandItem>
                )
              })}
              <div ref={scrollAreaRef} />
              {isFetchingNextPage && (
                <div className="flex items-center justify-center">
                  <Loader2 className="h-4 w-4 animate-spin" />
                </div>
              )}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}
