import { Checkbox } from '@ads/components-react'
import * as React from 'react'
import { useQuery } from 'react-query'

import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { useDebouncedValue } from '@/hooks/use-debounce'
import { LabelAndValue } from '@/model/labelAndValue'

import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from './command'
import { InputField } from './input-form'

// Backend response types
export interface BackendResponse<T = any> {
  data: T[]
  total?: number
  perPage?: number
  currentPage?: number
}

export interface BackendQueryParams {
  page?: number
  limit?: number
  q?: string
  [key: string]: any
}

export type BackendQueryFunction = (
  params: BackendQueryParams
) => Promise<BackendResponse<any>>

type MultiselectComboboxProps = {
  label?: string
  placeholder?: string
  searchPlaceholder?: string
  emptyMessage?: string
  loadingMessage?: string
  errorMessage?: string
  disabled?: boolean
  maxSelectedDisplay?: number
  className?: string
  selectAllLabel?: string
  clearAllLabel?: string
  showSelectAll?: boolean
  showClearAll?: boolean

  // Backend integration props
  queryKey: string | string[]
  queryFn: BackendQueryFunction
  transformData?: (data: any[]) => LabelAndValue[]
  searchDebounceMs?: number
  enableSearch?: boolean
  enablePagination?: boolean
  pageSize?: number
  additionalQueryParams?: Record<string, any>

  // Controlled component props
  value?: LabelAndValue[]
  onChange: (value: LabelAndValue[]) => void

  // Static options fallback (for backward compatibility)
  options?: LabelAndValue[]
}

export function Combobox({
  label,
  placeholder = 'Selecione opções...',
  searchPlaceholder = 'Pesquisar...',
  emptyMessage = 'Não encontrado.',
  loadingMessage = 'Carregando...',
  errorMessage,
  disabled = false,
  maxSelectedDisplay = 3,
  selectAllLabel = 'Selecionar todas',
  showSelectAll = true,
  showClearAll = true,

  // Backend integration props
  queryKey,
  queryFn,
  transformData,
  searchDebounceMs = 500,
  enableSearch = true,
  enablePagination = false,
  pageSize = 50,
  additionalQueryParams = {},

  // Controlled component props
  value = [],
  onChange,

  // Static options fallback
  options,
}: MultiselectComboboxProps) {
  const [open, setOpen] = React.useState(false)
  const [searchValue, setSearchValue] = React.useState('')
  const [currentPage, setCurrentPage] = React.useState(1)

  // Debounce search value for backend queries
  const debouncedSearchValue = useDebouncedValue(searchValue, searchDebounceMs)

  // Backend data fetching
  const {
    data: backendData,
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: Array.isArray(queryKey)
      ? [...queryKey, debouncedSearchValue, currentPage, additionalQueryParams]
      : [queryKey, debouncedSearchValue, currentPage, additionalQueryParams],
    queryFn: () =>
      queryFn({
        page: enablePagination ? currentPage : 1,
        limit: pageSize,
        q:
          enableSearch && debouncedSearchValue
            ? debouncedSearchValue
            : undefined,
        ...additionalQueryParams,
      }),
    enabled: !!queryFn && !options, // Only fetch if queryFn is provided and no static options
    keepPreviousData: true,
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: false,
  })

  // Transform backend data to LabelAndValue format
  const backendOptions = React.useMemo(() => {
    if (!backendData?.data) return []

    if (transformData) {
      return transformData(backendData.data)
    }

    // Default transformation - assumes data has 'id' and 'name' or 'title' fields
    return backendData.data.map((item: any) => ({
      value: String(item.id || item.value),
      label: item.name || item.title || item.label || String(item.id),
    }))
  }, [backendData, transformData])

  // Use backend options or fallback to static options
  const availableOptions = options || backendOptions

  const selectedValues = React.useMemo(
    () => new Set(value.map((item) => item.value)),
    [value]
  )

  // For static options, filter locally. For backend options, filtering is done server-side
  const filteredOptions = React.useMemo(() => {
    if (options) {
      // Static options - filter locally
      if (!searchValue) return options
      return options.filter((option) =>
        option.label.toLowerCase().includes(searchValue.toLowerCase())
      )
    }
    // Backend options - already filtered server-side
    return availableOptions
  }, [options, availableOptions, searchValue])

  const isAllSelected = React.useMemo(() => {
    return (
      filteredOptions.length > 0 &&
      filteredOptions.every((option) => selectedValues.has(option.value))
    )
  }, [filteredOptions, selectedValues])

  const handleSelect = React.useCallback(
    (option: LabelAndValue) => {
      const newValue = selectedValues.has(option.value)
        ? value.filter((item) => item.value !== option.value)
        : [...value, option]
      onChange(newValue)
    },
    [value, selectedValues, onChange]
  )

  const handleSelectAll = React.useCallback(() => {
    if (isAllSelected) {
      const filteredValues = new Set(filteredOptions.map((opt) => opt.value))
      const newValue = value.filter((item) => !filteredValues.has(item.value))
      onChange(newValue)
    } else {
      const newItems = filteredOptions.filter(
        (option) => !selectedValues.has(option.value)
      )
      onChange([...value, ...newItems])
    }
  }, [isAllSelected, filteredOptions, value, selectedValues, onChange])

  const displayText = React.useMemo(() => {
    if (value.length === 0) return placeholder
    if (value.length <= maxSelectedDisplay) {
      return value.map((item) => item.label).join(', ')
    }
    return `${value
      .slice(0, maxSelectedDisplay)
      .map((item) => item.label)
      .join(', ')} +${value.length - maxSelectedDisplay} mais`
  }, [value, maxSelectedDisplay, placeholder])

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger disabled={disabled} className="text-start">
        <InputField
          label={label}
          value={displayText}
          errorMessage={errorMessage}
          disabled={disabled}
          placeholder={placeholder}
          custom={{
            input:
              'h-10 w-full cursor-pointer truncate text-start text-ctx-content-base',
          }}
        />
      </PopoverTrigger>

      <PopoverContent
        className="w-[--radix-popover-trigger-width] rounded-lg bg-ctx-layout-spotlight p-1"
        align="start"
        asChild
      >
        <Command>
          <CommandInput
            className="outline-none"
            placeholder={searchPlaceholder}
            value={searchValue}
            onValueChange={setSearchValue}
          />
          <CommandList>
            <CommandEmpty>{emptyMessage}</CommandEmpty>
            <CommandGroup>
              {(showSelectAll || showClearAll) && (
                <>
                  {showSelectAll && filteredOptions.length > 0 && (
                    <CommandItem
                      onSelect={handleSelectAll}
                      className="text-ctx-content-title ts-paragraph-xxs"
                    >
                      <Checkbox
                        checked={isAllSelected}
                        custom={{ item: 'bg-ctx-interactive-secondary' }}
                      />
                      {selectAllLabel}
                    </CommandItem>
                  )}
                </>
              )}

              {filteredOptions.map((option) => {
                const isSelected = selectedValues.has(option.value)
                return (
                  <CommandItem
                    key={option.value}
                    value={option.label}
                    onSelect={() => handleSelect(option)}
                    className="text-ctx-content-title ts-paragraph-xxs"
                  >
                    <Checkbox
                      checked={isSelected}
                      custom={{ item: 'bg-ctx-interactive-secondary' }}
                    />
                    {option.label}
                  </CommandItem>
                )
              })}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}
