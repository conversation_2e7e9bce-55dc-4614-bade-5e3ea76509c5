import { Checkbox } from '@ads/components-react'
import * as React from 'react'

import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { LabelAndValue } from '@/model/labelAndValue'

import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from './command'
import { InputField } from './input-form'

type MultiselectComboboxProps = {
  label?: string
  placeholder?: string
  searchPlaceholder?: string
  emptyMessage?: string
  loadingMessage?: string
  errorMessage?: string
  disabled?: boolean
  isLoading?: boolean
  maxSelectedDisplay?: number
  className?: string
  selectAllLabel?: string
  clearAllLabel?: string
  showSelectAll?: boolean
  showClearAll?: boolean
  options: LabelAndValue[]
  value?: LabelAndValue[]
  onChange: (value: LabelAndValue[]) => void
  searchValue?: string
  onSearchChange?: (value: string) => void
  enableSearch?: boolean
  hasNextPage?: boolean
  onLoadMore?: () => void
  enablePagination?: boolean
}

export function AsyncCombobox({
  label,
  placeholder = 'Selecione opções...',
  searchPlaceholder = 'Pesquisar...',
  emptyMessage = 'Não encontrado.',
  loadingMessage = 'Carregando...',
  errorMessage,
  disabled = false,
  isLoading = false,
  maxSelectedDisplay = 3,
  selectAllLabel = 'Selecionar todas',
  showSelectAll = true,
  showClearAll = true,

  // Data props
  options,
  value = [],
  onChange,

  // Search props
  searchValue: externalSearchValue,
  onSearchChange,
  enableSearch = true,

  // Pagination props
  hasNextPage = false,
  onLoadMore,
  enablePagination = false,
}: MultiselectComboboxProps) {
  const [open, setOpen] = React.useState(false)
  const [internalSearchValue, setInternalSearchValue] = React.useState('')

  // Use external search value if provided, otherwise use internal state
  const searchValue =
    externalSearchValue !== undefined
      ? externalSearchValue
      : internalSearchValue

  const handleSearchChange = React.useCallback(
    (value: string) => {
      if (onSearchChange) {
        onSearchChange(value)
      } else {
        setInternalSearchValue(value)
      }
    },
    [onSearchChange]
  )

  const selectedValues = React.useMemo(
    () => new Set(value.map((item) => item.value)),
    [value]
  )

  // If parent component handles search, use options as-is
  // If internal search, filter options locally
  const filteredOptions = React.useMemo(() => {
    if (onSearchChange) {
      // Parent handles search - use options as provided
      return options
    }

    // Internal search - filter locally
    if (!searchValue) return options
    return options.filter((option) =>
      option.label.toLowerCase().includes(searchValue.toLowerCase())
    )
  }, [options, searchValue, onSearchChange])

  const isAllSelected = React.useMemo(() => {
    return (
      filteredOptions.length > 0 &&
      filteredOptions.every((option) => selectedValues.has(option.value))
    )
  }, [filteredOptions, selectedValues])

  const handleSelect = React.useCallback(
    (option: LabelAndValue) => {
      const newValue = selectedValues.has(option.value)
        ? value.filter((item) => item.value !== option.value)
        : [...value, option]
      onChange(newValue)
    },
    [value, selectedValues, onChange]
  )

  const handleSelectAll = React.useCallback(() => {
    if (isAllSelected) {
      const filteredValues = new Set(filteredOptions.map((opt) => opt.value))
      const newValue = value.filter((item) => !filteredValues.has(item.value))
      onChange(newValue)
    } else {
      const newItems = filteredOptions.filter(
        (option) => !selectedValues.has(option.value)
      )
      onChange([...value, ...newItems])
    }
  }, [isAllSelected, filteredOptions, value, selectedValues, onChange])

  const displayText = React.useMemo(() => {
    if (value.length === 0) return placeholder
    if (value.length <= maxSelectedDisplay) {
      return value.map((item) => item.label).join(', ')
    }
    return `${value
      .slice(0, maxSelectedDisplay)
      .map((item) => item.label)
      .join(', ')} +${value.length - maxSelectedDisplay} mais`
  }, [value, maxSelectedDisplay, placeholder])

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger disabled={disabled} className="text-start">
        <InputField
          label={label}
          value={displayText}
          errorMessage={errorMessage}
          disabled={disabled}
          placeholder={placeholder}
          custom={{
            input:
              'h-10 w-full cursor-pointer truncate text-start text-ctx-content-base',
          }}
        />
      </PopoverTrigger>

      <PopoverContent
        className="w-[--radix-popover-trigger-width] rounded-lg bg-ctx-layout-spotlight p-1"
        align="start"
        asChild
      >
        <Command>
          {enableSearch && (
            <CommandInput
              className="outline-none"
              placeholder={searchPlaceholder}
              value={searchValue}
              onValueChange={handleSearchChange}
            />
          )}
          <CommandList>
            {isLoading ? (
              <CommandEmpty>{loadingMessage}</CommandEmpty>
            ) : errorMessage ? (
              <CommandEmpty className="text-red-500">
                {errorMessage}
              </CommandEmpty>
            ) : filteredOptions.length === 0 ? (
              <CommandEmpty>{emptyMessage}</CommandEmpty>
            ) : null}

            {!isLoading && !errorMessage && (
              <CommandGroup>
                {(showSelectAll || showClearAll) && (
                  <>
                    {showSelectAll && filteredOptions.length > 0 && (
                      <CommandItem
                        onSelect={handleSelectAll}
                        className="text-ctx-content-title ts-paragraph-xxs"
                      >
                        <Checkbox
                          checked={isAllSelected}
                          custom={{ item: 'bg-ctx-interactive-secondary' }}
                        />
                        {selectAllLabel}
                      </CommandItem>
                    )}
                  </>
                )}

                {filteredOptions.map((option) => {
                  const isSelected = selectedValues.has(option.value)
                  return (
                    <CommandItem
                      key={option.value}
                      value={option.label}
                      onSelect={() => handleSelect(option)}
                      className="text-ctx-content-title ts-paragraph-xxs"
                    >
                      <Checkbox
                        checked={isSelected}
                        custom={{ item: 'bg-ctx-interactive-secondary' }}
                      />
                      {option.label}
                    </CommandItem>
                  )
                })}

                {/* Load more button for pagination */}
                {enablePagination && hasNextPage && onLoadMore && (
                  <CommandItem
                    onSelect={onLoadMore}
                    className="text-center text-ctx-content-base ts-paragraph-xxs"
                  >
                    Carregar mais...
                  </CommandItem>
                )}
              </CommandGroup>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}
