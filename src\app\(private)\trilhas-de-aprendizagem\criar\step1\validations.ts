import z from 'zod'

import { globalValidations } from '@/validations/global'

export const step1Schema = z
  .object({
    name: globalValidations.text,
    description: z.string().optional(),
    isMandatory: globalValidations.checkbox.optional(),
    duration: globalValidations.text.optional(),
    startDate: globalValidations.date.optional(),
    endDate: globalValidations.date.optional(),
  })
  .refine(
    (data) => {
      if (data.isMandatory) {
        return data.duration
      }
      return true
    },
    {
      message: 'Esse campo é obrigatório',
      path: ['duration'],
    }
  )
  .refine(
    (data) => {
      if (data.isMandatory && data.duration === '4') {
        return data.startDate
      }
      return true
    },
    {
      message: 'Esse campo é obrigatório',
      path: ['startDate'],
    }
  )
  .refine(
    (data) => {
      if (data.isMandatory && data.duration === '4') {
        return data.endDate
      }
      return true
    },
    {
      message: 'Esse campo é obrigatório',
      path: ['endDate'],
    }
  )
  .refine(
    (data) => {
      if (data.isMandatory && data.duration === '4') {
        if (!data.startDate || !data.endDate) return false
        return data.startDate < data.endDate
      }
      return true
    },
    {
      message: 'A data de término deve ser maior que a data de início',
      path: ['endDate'],
    }
  )
