import z from 'zod'

import { globalValidations } from '@/validations/global'

export const step1Schema = z
  .object({
    name: globalValidations.text,
    description: z.string().optional(),
    isMandatory: globalValidations.checkbox.optional(),
    duration: z.string().optional(),
    startDate: z.union([z.string(), z.date()]).optional(),
    endDate: z.union([z.string(), z.date()]).optional(),
  })
  .refine(
    (data) => {
      if (data.isMandatory) {
        return data.duration
      }
      return true
    },
    {
      message: 'Esse campo é obrigatório',
      path: ['duration'],
    }
  )
  .refine(
    (data) => {
      if (data.isMandatory && data.duration === '4') {
        return data.startDate && data.startDate !== ''
      }
      return true
    },
    {
      message: 'Esse campo é obrigatório',
      path: ['startDate'],
    }
  )
  .refine(
    (data) => {
      if (data.isMandatory && data.duration === '4') {
        return data.endDate && data.endDate !== ''
      }
      return true
    },
    {
      message: 'Esse campo é obrigatório',
      path: ['endDate'],
    }
  )
  .refine(
    (data) => {
      if (data.isMandatory && data.duration === '4') {
        if (
          !data.startDate ||
          !data.endDate ||
          data.startDate === '' ||
          data.endDate === ''
        )
          return true

        const startDate =
          typeof data.startDate === 'string'
            ? new Date(data.startDate)
            : data.startDate
        const endDate =
          typeof data.endDate === 'string'
            ? new Date(data.endDate)
            : data.endDate

        // Verifica se as datas são válidas
        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
          return false
        }

        return startDate < endDate
      }
      return true
    },
    {
      message: 'A data de término deve ser maior que a data de início',
      path: ['endDate'],
    }
  )
