import {
  Dropdown,
  DropdownItem,
  DropdownItemContent,
  Icon<PERSON><PERSON>on,
  useAlert,
} from '@ads/components-react'
import { Edit, EllipsisVertical } from 'lucide-react'
import Link from 'next/link'
import { useMutation, useQueryClient } from 'react-query'

import { DeleteCollaboratorsAlert } from '@/components/alerts-dialogs/delete-collaborators'
import { SendEmailCollaboratorsAlert } from '@/components/alerts-dialogs/send-email-collaborators'
import { UpdateCollaboratorsStatusAlert } from '@/components/alerts-dialogs/update-user-status'
import { Separator } from '@/components/ui/separator'
import { GetCollaboratorsQuery } from '@/graphql/generated/graphql'
import { activateB2bUser } from '@/http/collaborators/active-collaborator'
import { deleteCollaborator } from '@/http/collaborators/delete-collaborator'
import { sendWellcomeEmail } from '@/http/collaborators/send-wellcome-email'

type CollaboratorsDropdownOptionsProps = {
  collaborator: GetCollaboratorsQuery['users']['data'][number]
}

export function CollaboratorsDropdownOptions({
  collaborator,
}: CollaboratorsDropdownOptionsProps) {
  const queryClient = useQueryClient()
  const { alert } = useAlert()

  const { mutate: onActiveCollaborator } = useMutation({
    mutationFn: () =>
      activateB2bUser(
        collaborator.id,
        Number(collaborator.enrollments?.[0]?.id)
      ),
    onSuccess: () => {
      queryClient.invalidateQueries(['getCollaboratorsGql'])
      queryClient.invalidateQueries(['getLicensesCount'])
      alert({
        title: 'Colaborador ativado com sucesso!',
        description: `O(A) colaborador(a) ${collaborator.name} foi ativado(a) com sucesso.`,
        alertType: 'success',
      })
    },
    onError: () => {
      alert({
        title: 'Erro ao ativar colaborador',
        description: `Ocorreu um erro ao ativar colaborador(a) ${collaborator.name}. Tente novamente mais tarde.`,
        alertType: 'danger',
      })
    },
  })

  const { mutate: onDeleteCollaborator } = useMutation({
    mutationFn: ({ hasToDelete }: { hasToDelete: boolean }) =>
      deleteCollaborator({
        user_id: collaborator.id,
        enrollment_id: Number(collaborator.enrollments?.[0]?.id),
        has_to_cancel: hasToDelete,
      }),
    onSuccess: (_response, variables) => {
      queryClient.invalidateQueries(['getCollaboratorsGql'])
      queryClient.invalidateQueries(['getLicensesCount'])
      alert({
        title: `Colaborador ${variables.hasToDelete ? 'excluido(a)' : 'inativado(a)'} com sucesso!`,
        description: `O(A) colaborador(a) ${collaborator.name} foi ${variables.hasToDelete ? 'excluido(a)' : 'inativado(a)'} com sucesso.`,
        alertType: 'success',
      })
    },
    onError: () => {
      alert({
        title: 'Erro ao excluir colaborador',
        description: `Ocorreu um erro ao excluir colaborador(a) ${collaborator.name}. Tente novamente mais tarde.`,
        alertType: 'danger',
      })
    },
  })

  const { mutate: onSendEmail } = useMutation({
    mutationFn: () => sendWellcomeEmail(collaborator.id),
    onSuccess: () => {
      alert({
        title: 'E-mail enviado com sucesso',
        description: 'O e-mail de boas-vindas foi enviado com sucesso',
        alertType: 'success',
      })
    },
    onError: () => {
      alert({
        title: 'Erro ao enviar email',
        description:
          'Ocorreu um erro ao enviar email de boas vindas. Tente novamente mais tarde.',
        alertType: 'danger',
      })
    },
  })

  const status = collaborator.enrollments_pivot?.[0]?.status

  return (
    <Dropdown
      align="end"
      className="w-[216px]"
      trigger={
        <div className="flex w-full justify-end">
          <IconButton
            ariaLabel="Editar"
            icon={EllipsisVertical}
            hierarchy="tertiary"
            className="!p-0"
          />
        </div>
      }
    >
      <Link href={`/colaboradores/${collaborator.id}`} className="w-full">
        <DropdownItem>
          <DropdownItemContent leadingIcon={Edit}>Editar</DropdownItemContent>
        </DropdownItem>
      </Link>

      <SendEmailCollaboratorsAlert collaboratorsMutation={onSendEmail} />
      <UpdateCollaboratorsStatusAlert
        currentStatus={status === 'ACTIVE'}
        collaboratorsMutation={
          status === 'ACTIVE'
            ? () => onDeleteCollaborator({ hasToDelete: false })
            : onActiveCollaborator
        }
      />
      <Separator />

      <DeleteCollaboratorsAlert
        collaboratorsMutation={() =>
          onDeleteCollaborator({ hasToDelete: true })
        }
      />
    </Dropdown>
  )
}
