'use client'

import { <PERSON><PERSON>, Checkbox, Textarea } from '@ads/components-react'
import { zodResolver } from '@hookform/resolvers/zod'
import { useState } from 'react'
import { Controller, useForm } from 'react-hook-form'
import { HiOutlineClipboardDocumentCheck } from 'react-icons/hi2'
import Cookies from 'universal-cookie'

import { PerformaticCombobox } from '@/components/ui/comboxbox-performatic'
import { DatePicker } from '@/components/ui/date-picker'
import { InputField } from '@/components/ui/input-form'
import { Combobox } from '@/components/ui/multiselect-combobox'
import { SelectAds } from '@/components/ui/select-ads'
import { useDebouncedValue } from '@/hooks/use-debounce'

import { CancelNewStudyPlanAlert } from '../../criar/components/cancel-alert'
import { useStudyPlanQueries } from '../../criar/contexts/StudyPlanQueriesContext'
import { durationOptions } from './utils'
import { FormStudyPlanSchema, FormStudyPlanType } from './validations'

type FormStudyPlanProps = {
  handleSubmitForm: (data: FormStudyPlanType) => void
}

export function FormStudyPlan({ handleSubmitForm }: FormStudyPlanProps) {
  const [searchValue, setSearchValue] = useState('')
  const cookies = new Cookies(null, { path: '/' })
  const {
    register,
    control,
    handleSubmit,
    watch,
    resetField,
    formState: { errors },
  } = useForm<FormStudyPlanType>({
    resolver: zodResolver(FormStudyPlanSchema),
    defaultValues: {},
  })

  const { useTeamsQuery, useGroupsQuery, useCollaboratorsQuery } =
    useStudyPlanQueries()
  const { useCoursesQuery } = useStudyPlanQueries()
  const searchDebounce = useDebouncedValue(searchValue, 500)

  const {
    data: users,
    isLoading: isLoadingCollaborators,
    isFetching,
  } = useCollaboratorsQuery({
    searchValue,
    currentPage: 1,
    limit: 9999,
  })

  const { data: teams, isLoading: isLoadingTeams } = useTeamsQuery({
    currentPage: 1,
    limit: 9999,
  })

  const { data: groups, isLoading: isLoadingGroups } = useGroupsQuery({
    currentPage: 1,
    limit: 9999,
  })

  const { data: b2bCourses, isLoading: isLoadingCourses } = useCoursesQuery({
    currentPage: 1,
    limit: 9999,
  })

  const onSubmit = (data: FormStudyPlanType) => {
    handleSubmitForm(data)
  }

  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      className="space-y-3 rounded-lg bg-ctx-layout-body p-4 lg:p-8"
    >
      <h1 className="flex items-center gap-2 text-ctx-content-title ts-heading-sm">
        <HiOutlineClipboardDocumentCheck />
        Informações Básicas
      </h1>

      <section className="space-y-8 rounded-xl border border-ctx-layout-border p-4">
        <InputField
          id="name"
          label="Nome"
          type="text"
          placeholder="Nome da trilha de aprendizagem"
          className="w-full"
          {...register('name')}
          errorMessage={errors.name?.message}
        />

        <Textarea
          fullWidth
          label="Descrição"
          placeholder="Descrição da trilha de aprendizagem"
          custom={{
            textarea: 'bg-ctx-interactive-secondary',
          }}
          rows={6}
          {...register('description')}
        />

        <div className="grid grid-cols-1 gap-8 sm:grid-cols-2">
          <Controller
            name="courses"
            control={control}
            render={({ field }) => (
              <Combobox
                label="Cursos"
                placeholder="Selecione um ou mais Cursos para esta trilha"
                options={
                  b2bCourses?.courses.data.map((course) => ({
                    value: String(course.id),
                    label: course.title,
                  })) || []
                }
                onChange={field.onChange}
                value={field.value}
                errorMessage={errors.courses?.message}
                isLoading={isLoadingCourses}
              />
            )}
          />
          <Controller
            name="squads"
            control={control}
            render={({ field }) => (
              <Combobox
                label="Equipes"
                placeholder="Selecione uma ou mais Equipes para esta trilha"
                options={
                  teams?.companySquads.data.map(({ id, title }) => ({
                    value: String(id),
                    label: title,
                  })) || []
                }
                onChange={field.onChange}
                value={field.value}
                errorMessage={errors.squads?.message}
                isLoading={isLoadingTeams}
              />
            )}
          />
          <Controller
            name="groups"
            control={control}
            render={({ field }) => (
              <Combobox
                label="Grupos"
                placeholder="Selecione um ou mais Grupos para esta trilha"
                options={
                  groups?.companyGroups.data.map(({ id, name }) => ({
                    value: String(id),
                    label: name,
                  })) || []
                }
                onChange={field.onChange}
                value={field.value}
                errorMessage={errors.groups?.message}
                isLoading={isLoadingGroups}
              />
            )}
          />
          <Controller
            name="collaborators"
            control={control}
            render={({ field }) => (
              <PerformaticCombobox
                label="Colaboradores"
                placeholder="Selecione um ou mais Colaboradores para esta trilha"
                value={field.value || []}
                onChange={field.onChange}
                isLoading={isLoadingCollaborators || isFetching}
                errorMessage={errors.collaborators?.message}
                options={
                  users?.users.data.map(({ id, name }) => ({
                    value: String(id),
                    label: name,
                  })) || []
                }
              />
            )}
          />
        </div>
        <div className="space-y-4">
          <Controller
            name="isMandatory"
            control={control}
            render={({ field }) => (
              <Checkbox
                className="w-[145px]"
                label="Personalizar prazo"
                custom={{
                  item: 'bg-ctx-interactive-secondary',
                }}
                checked={field.value}
                onCheckedChange={field.onChange}
              />
            )}
          />
          <div className="flex w-full flex-col gap-4 lg:gap-8 xl:flex-row xl:items-center">
            {watch('isMandatory') && (
              <Controller
                name="duration"
                control={control}
                render={({ field }) => (
                  <div className="h-20 min-w-[208px] lg:w-[280px]">
                    <SelectAds
                      custom={{
                        trigger: 'h-10 bg-ctx-interactive-secondary',
                      }}
                      options={durationOptions}
                      label="Selecionar duração"
                      placeholder="Carga horária"
                      onValueChange={(event) => {
                        field.onChange(event)
                        resetField('startDate')
                        resetField('endDate')
                      }}
                      hasError={!!errors.duration}
                      defaultValue={field.value}
                      value={field.value}
                    />
                    <span className="text-red-500 ts-subtitle-xxxs">
                      {errors.duration?.message}
                    </span>
                  </div>
                )}
              />
            )}

            {watch('duration') === '4' && watch('isMandatory') && (
              <div className="flex w-full flex-col lg:flex-row lg:items-center lg:gap-4">
                <Controller
                  name="startDate"
                  control={control}
                  render={({ field }) => (
                    <DatePicker
                      label="Data de início"
                      placeholder="Selecione a Data de início"
                      value={field.value as Date}
                      onChange={field.onChange}
                      errorMessage={errors.startDate?.message}
                      classNameButton="w-[250px]"
                    />
                  )}
                />

                <span className="mb-2 text-ctx-content-base ts-paragraph-xxs lg:mb-0">
                  até
                </span>

                <Controller
                  name="endDate"
                  control={control}
                  render={({ field }) => (
                    <DatePicker
                      label="Data de término"
                      placeholder="Selecione a Data de término"
                      value={field.value as Date}
                      errorMessage={errors.endDate?.message}
                      onChange={field.onChange}
                      classNameButton="w-[250px]"
                    />
                  )}
                />
              </div>
            )}
          </div>
        </div>
      </section>

      <div className="flex w-full gap-2 sm:justify-end">
        <CancelNewStudyPlanAlert />

        <Button type="submit" className="flex-1 sm:flex-none">
          Cadastrar
        </Button>
      </div>
    </form>
  )
}
