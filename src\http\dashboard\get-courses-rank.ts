import { GET_DASHBOARD_COURSES_RANK } from '@/graphql/dashboard/queries'
import {
  GetDashboardCoursesRankQuery,
  GetDashboardCoursesRankQueryVariables,
} from '@/graphql/generated/graphql'
import { clientGraphql } from '@/services/graphql'

export const getCoursesRank = (data: GetDashboardCoursesRankQueryVariables) =>
  clientGraphql.request<GetDashboardCoursesRankQuery>(
    GET_DASHBOARD_COURSES_RANK,
    { ...data },
    {
      'cache-control': 'no-cache',
    }
  )
