'use client'

import Link from 'next/link'

import {
  DataTable,
  PaginationInfo,
  TableColumn,
} from '@/components/ui/data-table/data-table'
import { GetGroupsQuery } from '@/graphql/generated/graphql'
import { convertDateToCalendar } from '@/utils/convert-date'

import { GroupDropdownOptions } from './dropdown-options'

type GroupsTableProps = {
  groups: GetGroupsQuery['companyGroups']
  isLoading: boolean
  currentPage: number
  setCurrentPage: (page: number) => void
  isFetchingNewPage?: boolean
}

export function GroupsTable({
  groups,
  isLoading,
  currentPage,
  isFetchingNewPage = false,
  setCurrentPage,
}: GroupsTableProps) {
  const paginationInfo: PaginationInfo = {
    currentPage,
    totalPages: Math.ceil(groups.total / groups.perPage) || 1,
    itemsPerPage: groups.perPage,
    totalItems: groups.total,
  }

  const columns: TableColumn<
    GetGroupsQuery['companyGroups']['data'][number]
  >[] = [
    {
      key: 'title',
      header: 'Nome da Grupo',
      headerClassName: 'text-nowrap',
      accessor: ({ id, name }) => <Link href={`/grupos/${id}`}>{name}</Link>,
    },
    {
      key: 'users_count',
      header: 'Colaboradores',
      accessor: 'users_count',
      headerClassName: 'text-center text-nowrap',
      className: 'text-center',
    },
    {
      key: 'updated_at',
      header: 'Última Atualização',
      accessor: ({ updated_at }) => (
        <p>{convertDateToCalendar(updated_at) || '-'}</p>
      ),
      headerClassName: 'text-center text-nowrap',
      className: 'text-center',
    },
    {
      key: 'options',
      header: '',
      accessor: (group) => <GroupDropdownOptions group={group} />,
    },
  ]

  const handleChangePage = (page: number) => {
    setCurrentPage(page)
  }

  return (
    <DataTable
      data={groups.data}
      columns={columns}
      pagination={paginationInfo}
      onPageChange={handleChangePage}
      loading={isLoading}
      isFetchingNewPage={isFetchingNewPage}
      emptyMessage="Nenhum grupo encontrado."
    />
  )
}
