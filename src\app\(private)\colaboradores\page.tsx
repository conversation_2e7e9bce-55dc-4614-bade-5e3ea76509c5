'use client'

import { <PERSON><PERSON>, <PERSON> } from '@ads/components-react'
import Link from 'next/link'
import { useEffect, useMemo, useState } from 'react'
import { useQuery } from 'react-query'
import Cookies from 'universal-cookie'

import { PageLayout } from '@/components/layouts/page-layout'
import { CollaboratorsTable } from '@/components/tables/collaborators'
import { EListOrder, EListOrderBy } from '@/graphql/generated/graphql'
import { useDebouncedValue } from '@/hooks/use-debounce'
import { getCollaborators } from '@/http/collaborators/get-collaborators'

import { EmptyList } from './empty-list'
import { ImportCollaborators } from './import-collaboratorts'
import { LicensesStatus } from './licenses-status'

export default function Users() {
  const { companyId, enrollmentId } = useMemo(() => {
    const cookies = new Cookies(null, { path: '/' })
    return {
      companyId: cookies.get('b2bCompanyId'),
      enrollmentId: Number(cookies.get('b2bEnrollmentId')),
    }
  }, [])

  const [currentPage, setCurrentPage] = useState(1)
  const [searchValue, setSearchValue] = useState<string>()

  const debouncedSearchValue = useDebouncedValue(searchValue, 500)

  const {
    data: collaborators,
    isFetching,
    isLoading,
  } = useQuery({
    queryKey: [
      'getCollaboratorsGql',
      debouncedSearchValue,
      currentPage,
      companyId,
      enrollmentId,
    ],
    queryFn: () =>
      getCollaborators({
        page: currentPage,
        limit: 10,
        company_id: companyId,
        enrollment_id: enrollmentId,
        order: EListOrder.ASC,
        orderBy: EListOrderBy.NAME,
        q: debouncedSearchValue ?? '',
      }),
    keepPreviousData: true,
    refetchOnWindowFocus: false,
  })

  const isLoadingTable = currentPage === 1 && (isLoading || isFetching)
  const isFetchingTable = currentPage !== 1 && (isLoading || isFetching)

  useEffect(() => {
    setCurrentPage(1)
  }, [debouncedSearchValue, setCurrentPage])

  return (
    <PageLayout
      title="Colaboradores"
      description="Gerencie os colaboradores desta organização."
    >
      <LicensesStatus />

      <div className="mt-5 flex flex-col-reverse items-center justify-between gap-5 md:mt-8 lg:flex-row">
        <div className="w-full flex-1 lg:max-w-[383px] [&_button.at-rounded-component-iconButton-border-radius]:hidden">
          <Search
            size="lg"
            placeholder="Buscar por Nome ou E-mail"
            className="bg-ctx-interactive-secondary [&_button.at-rounded-component-iconButton-border-radius]:hidden"
            handleChange={(e) => setSearchValue(e)}
            query={searchValue}
          />
        </div>
        <div className="flex w-full flex-col items-center gap-4 lg:w-auto lg:flex-row">
          <ImportCollaborators />
          <Link href="/colaboradores/novo" className="w-full lg:w-auto">
            <Button size="md" className="w-full lg:w-auto">
              Cadastrar Colaborador
            </Button>
          </Link>
        </div>
      </div>

      <div className="mt-5 md:mt-8">
        {!isLoading && !isFetching && !collaborators?.users.data.length ? (
          <EmptyList />
        ) : (
          <CollaboratorsTable
            isLoading={isLoadingTable}
            isFetchingNewPage={isFetchingTable}
            currentPage={currentPage}
            setCurrentPage={setCurrentPage}
            collaborators={
              collaborators?.users ?? { data: [], total: 0, perPage: 0 }
            }
          />
        )}
      </div>
    </PageLayout>
  )
}
