'use client'

import { But<PERSON> } from '@ads/components-react'
import { Controller, useFormContext } from 'react-hook-form'

import { StudyPlanStatus } from '@/model/study-plan'

import { SelectAds } from '../ui/select-ads'

const statusOptions: { value: StudyPlanStatus; label: string }[] = [
  {
    value: 'ALL',
    label: 'Todos Status',
  },
  {
    value: 'ACTIVE',
    label: 'Ativo',
  },
  {
    value: 'INACTIVE',
    label: 'Inativo',
  },
  {
    value: 'FINISHED',
    label: 'Finalizado',
  },
]

type DropdownMenuProps = {
  onSubmit: () => void
  clearFilters: () => void
}

export function DropdownMenu({ onSubmit, clearFilters }: DropdownMenuProps) {
  const { control } = useFormContext()

  return (
    <div className="space-y-6 p-2">
      <div className="z-40 flex flex-col space-y-4 md:w-full md:flex-row md:space-x-6 md:space-y-0">
        <Controller
          name="status"
          control={control}
          render={({ field }) => (
            <SelectAds
              label="Status"
              size="md"
              fullWidth
              custom={{
                content: 'z-40 ',
              }}
              onValueChange={field.onChange}
              defaultValue={field.value}
              value={field.value}
              options={statusOptions}
            />
          )}
        />
      </div>

      <div className="flex flex-col items-center md:flex-row md:items-baseline md:justify-end md:space-x-6">
        <Button
          type="button"
          hierarchy="tertiary"
          className="whitespace-nowrap"
          onClick={clearFilters}
        >
          Limpar filtros
        </Button>

        <Button
          type="button"
          hierarchy="primary"
          className="w-full md:w-32"
          onClick={onSubmit}
        >
          Aplicar
        </Button>
      </div>
    </div>
  )
}
