import {
  Dropdown,
  DropdownItem,
  DropdownI<PERSON><PERSON>ontent,
  I<PERSON><PERSON><PERSON>on,
  useAlert,
} from '@ads/components-react'
import { Edit, EllipsisVertical } from 'lucide-react'
import Link from 'next/link'
import { useMutation, useQueryClient } from 'react-query'

import { DeleteGroupAlert } from '@/components/alerts-dialogs/delete-group'
import { Separator } from '@/components/ui/separator'
import { GetGroupsQuery } from '@/graphql/generated/graphql'
import { deleteGroup } from '@/http/groups'

type TeamDropdownOptionsProps = {
  group: GetGroupsQuery['companyGroups']['data'][number]
}

export function GroupDropdownOptions({ group }: TeamDropdownOptionsProps) {
  const { alert } = useAlert()
  const queryClient = useQueryClient()

  const { mutate: onDeleteMutation } = useMutation({
    mutationFn: () => deleteGroup(group.id),
    onSuccess: () => {
      queryClient.refetchQueries(['getGroupsGql'], {
        active: true,
        exact: false,
      })
      alert({
        title: 'Grupo excluído com sucesso!',
        description: `O grupo ${group.name} foi excluído com sucesso.`,
        alertType: 'success',
      })
    },
    onError: () => {
      alert({
        title: 'Erro ao excluir grupo',
        description: `Ocorreu um erro ao excluir o grupo ${group.name}. Tente novamente mais tarde.`,
        alertType: 'danger',
      })
    },
  })

  return (
    <Dropdown
      className="w-[216px]"
      align="end"
      trigger={
        <div className="flex w-full justify-end">
          <IconButton
            ariaLabel="Editar"
            icon={EllipsisVertical}
            hierarchy="tertiary"
            className="!p-0"
          />
        </div>
      }
    >
      <Link href={`/grupos/${group.id}`} className="w-full">
        <DropdownItem>
          <DropdownItemContent leadingIcon={Edit}>Editar</DropdownItemContent>
        </DropdownItem>
      </Link>
      <Separator />

      <DeleteGroupAlert groupMutation={onDeleteMutation} />
    </Dropdown>
  )
}
