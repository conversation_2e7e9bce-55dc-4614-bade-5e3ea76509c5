import { cn } from '@/lib/utils'

type PageLayoutProps = {
  title: string
  description?: string
  children: React.ReactNode
  actionButton?: React.ReactNode
  className?: string
}

export function PageLayout({
  children,
  title,
  description,
  actionButton,
  className = '',
}: PageLayoutProps) {
  return (
    <div className={cn('space-y-8 px-4 py-6 @container md:px-8', className)}>
      <div className="flex flex-col items-center justify-between gap-4 sm:flex-row">
        <div>
          <h1 className="text-ctx-content-title ts-heading-sm">{title}</h1>
          {description && (
            <span className="hidden text-ctx-content-base ts-paragraph-xxs @[480px]:block">
              {description}
            </span>
          )}
        </div>
        {actionButton}
      </div>
      {children}
    </div>
  )
}
