import { cn } from '@/lib/utils'

type PageLayoutProps = {
  title: string
  description?: string
  children: React.ReactNode
  actionButton?: React.ReactNode
  className?: string
}

export function PageLayout({
  children,
  title,
  description,
  actionButton,
  className = '',
}: PageLayoutProps) {
  return (
    <div className={cn('space-y-8 px-4 py-6 md:px-8', className)}>
      <div className="flex flex-col items-center justify-between gap-4 sm:flex-row">
        <div>
          <h1 className="text-ctx-content-title ts-heading-md">{title}</h1>
          {description && (
            <span className="text-ctx-content-base ts-paragraph-xs">
              {description}
            </span>
          )}
        </div>
        {actionButton}
      </div>
      {children}
    </div>
  )
}
