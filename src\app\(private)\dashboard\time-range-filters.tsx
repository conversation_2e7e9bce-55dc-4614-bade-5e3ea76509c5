import { Button } from '@ads/components-react'
import { useEffect, useState } from 'react'

import { DatePicker } from '@/components/ui/date-picker'
import { timePeriodFilters, TimePeriodFilterTypes } from '@/consts/dashboard'
import { getEndOfDay, getStartOfDay } from '@/utils/convert-date'

interface TimeRangeFiltersProps {
  timePeriod: TimePeriodFilterTypes
  onChangeTimeRange: (timePeriod: TimePeriodFilterTypes) => void
}

export function TimeRangeFilters({
  timePeriod,
  onChangeTimeRange,
}: TimeRangeFiltersProps) {
  const [customizeDate, setCustomizeDate] = useState<{
    start_date?: Date
    end_date?: Date
  }>({})

  useEffect(() => {
    if (timePeriod.label !== 'Personalizado') {
      setCustomizeDate({})
    }
  }, [timePeriod])

  useEffect(() => {
    setCustomizeDate((prev) => ({ ...prev, end_date: undefined }))
  }, [customizeDate.start_date])

  useEffect(() => {
    if (customizeDate.start_date && customizeDate.end_date) {
      onChangeTimeRange({
        label: 'Personalizado',
        personalized: true,
        values: {
          start_date: getStartOfDay(customizeDate.start_date, { utc: true }),
          end_date: getEndOfDay(customizeDate.end_date),
        },
      })
    }
  }, [customizeDate.end_date, onChangeTimeRange])

  return (
    <div className="flex flex-col flex-nowrap gap-2 sm:flex-row sm:flex-wrap sm:items-center">
      {/* Botões */}
      <div className="flex flex-wrap gap-2 sm:flex-row">
        {timePeriodFilters.map((data) => (
          <Button
            key={data.label}
            hierarchy={
              data.label === timePeriod.label ? 'primary' : 'secondary'
            }
            onClick={() => onChangeTimeRange(data)}
            className="flex-1 text-nowrap sm:flex-none"
          >
            {data.label}
          </Button>
        ))}
      </div>

      {/* DatePickers */}
      {timePeriod.personalized && (
        <div className="ml-0 mt-2 flex flex-col sm:ml-4 sm:mt-0 sm:flex-row sm:gap-4">
          <DatePicker
            label="Data de início"
            placeholder="Selecione a data de início"
            classNameButton="w-full sm:w-fit"
            value={customizeDate.start_date}
            onChange={(e) =>
              setCustomizeDate((prev) => ({
                ...prev,
                start_date: e,
              }))
            }
          />
          <DatePicker
            label="Data de término"
            placeholder="Selecione a data de término"
            classNameButton="w-full sm:w-fit"
            disabled={!customizeDate.start_date}
            disabledDate={(date) =>
              customizeDate.start_date ? date < customizeDate.start_date : false
            }
            value={customizeDate.end_date}
            onChange={(e) =>
              setCustomizeDate((prev) => ({
                ...prev,
                end_date: e,
              }))
            }
          />
        </div>
      )}
    </div>
  )
}
