'use client'

import { useQuery } from 'react-query'
import {
  Bar,
  BarChart,
  Cell,
  ResponsiveContainer,
  Tooltip,
  TooltipProps,
  XAxis,
  YAxis,
} from 'recharts'

import { SkeletonCoursesRank } from '@/components/loaders/skeletons/courses-rank'
import { ChartContainer } from '@/components/ui/chart'
import { useAuth } from '@/contexts/AuthContext'
import { useIsMobile } from '@/hooks/use-mobile'
import { getCoursesRank } from '@/http/dashboard/get-courses-rank'

import { EmptyChart } from './empty-chart'

interface CoursesRankProps {
  start_date?: Date
  end_date?: Date
}

export function CoursesRank({ start_date, end_date }: CoursesRankProps) {
  const { user } = useAuth()
  const isMobile = useIsMobile()

  const {
    data: dashboard,
    isLoading,
    isFetching,
  } = useQuery({
    queryKey: ['getCoursesRank', start_date, end_date],
    queryFn: () =>
      getCoursesRank({
        start_date,
        end_date,
        company_id: Number(user?.metadata.company_id),
      }),
    enabled: !!end_date && !!user?.metadata.company_id,
    keepPreviousData: true,
  })

  const data = dashboard?.b2bStatistics.mostWatchedCoursesRanking || []

  const colors = data?.map((_, index) => {
    const alpha = 1 - index * (1 / data.length)
    return `rgba(255, 198, 41, ${alpha.toFixed(2)})`
  })

  const chartData = data?.map((item, index) => ({
    name: item.course.title,
    views: item.views,
    fill: colors[index],
  }))

  const isLoadingData = isFetching || isLoading || !user?.metadata.company_id

  const CustomTooltip = (props: TooltipProps<string, string>) => {
    const { active, payload, coordinate } = props

    if (!active || !payload?.length) return null

    const item = payload[0]?.payload

    return (
      <div
        className="w-screen max-w-[200px] rounded-2xl bg-[#364152] p-3 shadow-md"
        style={{
          position: 'absolute',
          left: coordinate?.x,
          top: coordinate?.y ?? 0,
          transform: 'translate(-50%, -100%)',
        }}
      >
        <p className="text-center text-ctx-content-baseAlternative ts-paragraph-xxxs">
          {item?.name}
        </p>
        <p className="mt-2 text-center text-ctx-content-baseAlternative ts-paragraph-xxxs">
          {item?.views} {item?.views > 1 ? 'acessos' : 'acesso'}
        </p>
      </div>
    )
  }

  if (isLoadingData) {
    return <SkeletonCoursesRank />
  }

  if (data.length === 0) {
    return <EmptyChart />
  }

  return (
    <ChartContainer
      className="mt-5 h-[250px] w-full sm:h-[500px]"
      config={{
        views: { label: 'Visualizações' },
      }}
    >
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={chartData}
          margin={{ top: 20, right: 10, left: 0, bottom: 20 }}
        >
          {!isMobile && (
            <XAxis
              dataKey="name"
              tick={(props) => {
                const { x, y, payload, width } = props
                const text = payload.value as string

                const numBars = chartData.length
                const approxBarWidth = (width - numBars * 10) / numBars
                const approxCharWidth = 7
                const maxChars = Math.floor(approxBarWidth / approxCharWidth)

                const label =
                  text.length > maxChars
                    ? text.slice(0, maxChars - 3) + '...'
                    : text

                return (
                  <text
                    x={x}
                    y={y + 15}
                    textAnchor="middle"
                    fontSize={12}
                    fill="#fff"
                  >
                    {label}
                  </text>
                )
              }}
              interval={0}
              textAnchor="middle"
              height={80}
            />
          )}

          <YAxis width={40} tick={{ fontSize: isMobile ? 10 : 14 }} />

          <Tooltip
            content={<CustomTooltip />}
            cursor={{ fill: 'transparent' }}
            position={undefined}
          />

          <Bar dataKey="views" radius={[8, 8, 0, 0]}>
            {chartData.map((entry, index) => (
              <Cell key={`bar-${index}`} fill={entry.fill} />
            ))}
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    </ChartContainer>
  )
}
