import z from 'zod'

import { step1Schema } from './step1/validations'

export const getDatePlusDays = (days: number) => {
  const date = new Date()
  date.setDate(date.getDate() + days)
  return date.toISOString()
}

// O value representa a quantidade de meses em dias
export const durationOptions = [
  { value: '30', label: '1 mês' },
  { value: '90', label: '3 meses' },
  { value: '180', label: '6 meses' },
  { value: '4', label: 'Personalizado' },
]

export const CombinedCheckoutSchema = step1Schema

export type CombinedCheckoutType = z.infer<typeof CombinedCheckoutSchema>
