import z from 'zod'

import { step1Schema } from './step1/validations'

const today = new Date()

export const durationOptions = [
  { value: new Date().setDate(today.getDate() + 30), label: '1 mês' },
  { value: new Date().setDate(today.getDate() + 90), label: '3 meses' },
  { value: new Date().setDate(today.getDate() + 180), label: '6 meses' },
  { value: '4', label: 'Personalizado' },
]

export const CombinedCheckoutSchema = step1Schema

export type CombinedCheckoutType = z.infer<typeof CombinedCheckoutSchema>
