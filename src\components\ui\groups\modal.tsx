import { Button, Checkbox, Search } from '@ads/components-react'
import { X } from 'lucide-react'
import { useEffect, useMemo, useState } from 'react'
import { useQuery } from 'react-query'
import Cookies from 'universal-cookie'

import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import { ScrollArea } from '@/components/ui/scroll-area'
import { useDebouncedValue } from '@/hooks/use-debounce'
import { getB2bUsers } from '@/http/user'
import { useSelectedUsersGroups } from '@/store/useSelectedUsersGroup'

import { GroupModalTable } from '../../tables/group/users-modal'
import { Separator } from '../separator'

export function AddUsersModal() {
  const [currentPage, setCurrentPage] = useState(1)
  const [searchValue, setSearchValue] = useState('')

  const debouncedSearchValue = useDebouncedValue(searchValue, 500)

  const { companyId, enrollmentId } = useMemo(() => {
    const cookies = new Cookies(null, { path: '/' })
    return {
      companyId: cookies.get('b2bCompanyId'),
      enrollmentId: cookies.get('b2bEnrollmentId'),
    }
  }, [])

  const {
    selectedUserIds,
    tempSelectedUserIds,
    tempSelectedAll,
    selectedAll,
    setSelected,
    setTempSelected,
    setSelectedAll,
    setTempSelectedAll,
  } = useSelectedUsersGroups()

  const {
    data: usersByCompany,
    isLoading,
    isFetching,
  } = useQuery({
    queryKey: ['getb2bUsersGql', currentPage, debouncedSearchValue],
    queryFn: () =>
      getB2bUsers({
        page: currentPage,
        limit: 10,
        orderBy: 'NAME',
        company_id: companyId,
        enrollment_id: Number(enrollmentId),
        q: searchValue || undefined,
      }),
    keepPreviousData: true,
    staleTime: 60 * 1000 * 10, // 10 minutes
  })

  const handleResetPagination = () => {
    setCurrentPage(1)
  }

  const handleCloseModal = () => {
    handleResetPagination()
    setTempSelected(selectedUserIds)
    setTempSelectedAll(selectedAll)
  }

  const handleChangeSelectedAllUsers = () => {
    handleResetPagination()
    setTempSelected([])
    setTempSelectedAll(!tempSelectedAll)
  }

  const handleSelect = () => {
    handleResetPagination()
    setSelected(tempSelectedUserIds)
    setSelectedAll(tempSelectedAll)
  }

  useEffect(() => {
    setCurrentPage(1)
  }, [debouncedSearchValue, setCurrentPage])

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button className="w-full md:w-auto" size="md">
          Selecionar colaboradores
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent className="h-[700px] max-w-[600px] overflow-hidden rounded-lg bg-ctx-layout-surface">
        <AlertDialogHeader>
          <div className="flex justify-between">
            <div>
              <AlertDialogTitle className="mb-1 text-ctx-content-title ts-heading-sm">
                Selecionar colaboradores
              </AlertDialogTitle>
              <AlertDialogDescription className="text-ctx-content-base ts-paragraph-xxs">
                Selecione colaboradores para fazer parte desta equipe
              </AlertDialogDescription>
            </div>
            <AlertDialogCancel
              onClick={handleCloseModal}
              className="h-8 w-8 border-none"
            >
              <X />
            </AlertDialogCancel>
          </div>
        </AlertDialogHeader>

        <Separator className="-mx-[25px] w-[calc(100%+50px)]" />
        <div className="w-full max-w-[379px]">
          <Search
            size="lg"
            placeholder="Buscar por Buscar por nome ou e-mail"
            className="bg-ctx-interactive-secondary [&_button.at-rounded-component-iconButton-border-radius]:hidden"
            handleChange={(e) => setSearchValue(e)}
            query={searchValue}
          />
        </div>
        <div className="flex items-center gap-2">
          <Checkbox
            checked={tempSelectedAll}
            onCheckedChange={handleChangeSelectedAllUsers}
            className="dark:rounded-sm dark:bg-[#FFFFFFB8]"
            size="sm"
          />
          <p className="text-ctx-content-base ts-heading-xxs">
            Selecionar todos os colaboradores
          </p>
        </div>

        <ScrollArea className="rounded-md border">
          <GroupModalTable
            isLoading={isLoading}
            isFetchingNewPage={isFetching}
            currentPage={currentPage}
            setCurrentPage={setCurrentPage}
            usersByCompany={
              usersByCompany?.users ?? { data: [], total: 0, perPage: 0 }
            }
          />
        </ScrollArea>

        <div className="flex w-full justify-end">
          <AlertDialogFooter>
            <AlertDialogCancel asChild>
              <Button onClick={handleSelect} size="lg">
                Selecionar
              </Button>
            </AlertDialogCancel>
          </AlertDialogFooter>
        </div>
      </AlertDialogContent>
    </AlertDialog>
  )
}
