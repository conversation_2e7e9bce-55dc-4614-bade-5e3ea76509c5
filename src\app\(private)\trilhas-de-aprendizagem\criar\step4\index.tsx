'use client'

import { HiOutlineClipboardDocumentCheck } from 'react-icons/hi2'
import { useQuery, useQueryClient } from 'react-query'

import { ScrollArea } from '@/components/ui/scroll-area'
import { ESolutionContext } from '@/enum/solution-context'
import { getCollaborators } from '@/http/collaborators/get-collaborators'
import { getCourses } from '@/http/courses'
import { getGroups } from '@/http/groups'
import { getTeams } from '@/http/teams/get-teams'
import { useSelectedCollaboratorsTrail } from '@/store/useSelectedCollaboratorsTrail'
import { useSelectedCoursesTrail } from '@/store/useSelectedCoursesTrail'
import { useSelectedGroupsTrail } from '@/store/useSelectedGroupsTrail'
import { useSelectedSquadsTrail } from '@/store/useSelectedSquadsTrail'
import { useStepOneSaved } from '@/store/useStepOneSaved'
import { convertDateToCalendar } from '@/utils/convert-date'

import { durationOptions } from '../validations'
import { FieldLayout } from './components/fieldLayout'

const totalCollaborators = 10
const totalGroups = 10
const totalSquads = 10

export function Step4() {
  const { data: step1 } = useStepOneSaved()
  const { coursesSelected } = useSelectedCoursesTrail()
  const { allSquads, excludedSquads, squadsSelected } = useSelectedSquadsTrail()
  const { groupsSelected, allGroups, excludedGroups } = useSelectedGroupsTrail()
  const { collaboratorsSelected, allCollaborators, excludedCollaborators } =
    useSelectedCollaboratorsTrail()

  const totalCollaboratorsSelected = allCollaborators
    ? totalCollaborators - excludedCollaborators.length
    : collaboratorsSelected.length

  const totalGroupsSelected = allGroups
    ? totalGroups - excludedGroups.length
    : groupsSelected.length

  const totalSquadsSelected = allSquads
    ? totalSquads - excludedSquads.length
    : squadsSelected.length

  const foundDuration = durationOptions.find(
    (option) => option.value === (step1?.duration as string)
  )

  const convertedStartDate =
    step1?.startDate && convertDateToCalendar(step1?.startDate)
  const convertedEndDate =
    step1?.endDate && convertDateToCalendar(step1?.endDate)

  const { data: b2bCourses } = useQuery({
    queryKey: ['studyPlanCourses'],
    queryFn: () =>
      getCourses({
        page: 1,
        limit: 10,
        all: false,
        available_at: ESolutionContext.PRO.toLowerCase() as ESolutionContext,
      }),
    refetchOnWindowFocus: false,
    staleTime: 1000 * 60 * 5,
  })

  const { data: collaborators } = useQuery({
    queryKey: ['getCollaboratorsGql'],
    queryFn: () =>
      getCollaborators({
        page: 1,
        limit: 10,
        company_id: companyId,
        enrollment_id: enrollmentId,
      }),
    refetchOnWindowFocus: false,
    staleTime: 1000 * 60 * 5,
  })

  const { data: teams } = useQuery({
    queryKey: ['getTeamsGql'],
    queryFn: () =>
      getTeams({
        company_id: companyId,
        page: currentPage,
        limit: 10,
      }),
    refetchOnWindowFocus: false,
    staleTime: 1000 * 60 * 5,
  })

  const { data: groups } = useQuery({
    queryKey: ['getGroupsGql'],
    queryFn: () =>
      getGroups({
        company_id: companyId,
        page: currentPage,
        limit: 10,
      }),

    refetchOnWindowFocus: false,
    staleTime: 1000 * 60 * 5,
  })

  const totalCourses = b2bCourses?.courses.total

  console.log(b2bCourses?.courses.total)

  return (
    <div className="space-y-8 rounded-lg bg-ctx-layout-body p-4 lg:p-8">
      <h1 className="flex items-center gap-2 text-ctx-content-title ts-heading-sm">
        <HiOutlineClipboardDocumentCheck />
        Resumo
      </h1>

      <section className="space-y-8 rounded-xl">
        <FieldLayout defaultValue={step1?.name} title="Nome da trilha" />
        {step1?.description && (
          <FieldLayout
            title="Descrição da trilha"
            defaultValue={step1?.description}
          />
        )}
        <FieldLayout
          title="Prazo"
          defaultValue={
            foundDuration?.value === '4' // it means custom dates
              ? `${convertedStartDate} até ${convertedEndDate}`
              : (foundDuration?.label as string)
          }
        />
        <section className="space-y-2 rounded-xl border border-ctx-layout-border p-4">
          <ScrollArea className="h-24 w-full rounded-lg border border-border bg-ctx-interactive-secondary p-2 px-4 text-ctx-content-base">
            <ul className="space-y-1.5 ts-heading-xxs">
              <li className="font-extrabold">
                Colaboradores: {totalCollaboratorsSelected}
              </li>

              <li className="font-extrabold">Equipes: {totalSquadsSelected}</li>

              <li className="font-extrabold">Grupos: {totalGroupsSelected}</li>
            </ul>
          </ScrollArea>
        </section>
        <section className="space-y-2 rounded-xl border border-ctx-layout-border p-4">
          <ScrollArea className="h-24 w-full rounded-lg border border-border bg-ctx-interactive-secondary p-2 px-4 text-ctx-content-base">
            <ul className="space-y-1.5 ts-heading-xxs">
              <li className="font-extrabold">
                Cursos: {coursesSelected.length}
              </li>
            </ul>
          </ScrollArea>
        </section>
      </section>
    </div>
  )
}
