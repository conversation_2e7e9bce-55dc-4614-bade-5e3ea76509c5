'use client'

import { HiOutlineClipboardDocumentCheck } from 'react-icons/hi2'

import { useSelectedCollaboratorsTrail } from '@/store/useSelectedCollaboratorsTrail'
import { useSelectedCoursesTrail } from '@/store/useSelectedCoursesTrail'
import { useSelectedGroupsTrail } from '@/store/useSelectedGroupsTrail'
import { useSelectedSquadsTrail } from '@/store/useSelectedSquadsTrail'

import { FieldLayout } from './components/fieldLayout'

const totalCollaborators = 10

export function Step4() {
  const { coursesSelected } = useSelectedCoursesTrail()
  const { collaboratorsSelected, allCollaborators, excludedCollaborators } =
    useSelectedCollaboratorsTrail()
  const { squadsSelected, allSquads, excludedSquads } = useSelectedSquadsTrail()
  const { allGroups, excludedGroups, groupsSelected } = useSelectedGroupsTrail()

  const totalCollaboratorsSelected = allCollaborators
    ? totalCollaborators - excludedCollaborators.length
    : collaboratorsSelected.length

  const totalSquadsSelected = coursesSelected.length

  return (
    <div className="space-y-8 rounded-lg bg-ctx-layout-body p-4 lg:p-8">
      <h1 className="flex items-center gap-2 text-ctx-content-title ts-heading-sm">
        <HiOutlineClipboardDocumentCheck />
        Resumo
      </h1>

      <section className="space-y-8 rounded-xl">
        <FieldLayout defaultValue="Nome da trilha" title="Nome da trilha" />
        <FieldLayout title="Descrição da trilha" defaultValue="asdasdasd" />
        <FieldLayout title="Prazo" defaultValue="Descrição da trilha" />
        <FieldLayout
          isTextArea
          title="Participantes"
          defaultValue={{
            collaborators: 90,
            teams: 80,
            groups: 70,
          }}
        />
        <FieldLayout
          title="Cursos"
          defaultValue={totalSquadsSelected}
          isTextArea
        />
      </section>
    </div>
  )
}
