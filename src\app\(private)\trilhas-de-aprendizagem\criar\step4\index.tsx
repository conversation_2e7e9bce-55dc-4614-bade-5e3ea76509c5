'use client'

import { HiOutlineClipboardDocumentCheck } from 'react-icons/hi2'

import { ScrollArea } from '@/components/ui/scroll-area'
import { useSelectedCollaboratorsTrail } from '@/store/useSelectedCollaboratorsTrail'
import { useSelectedCoursesTrail } from '@/store/useSelectedCoursesTrail'
import { useSelectedGroupsTrail } from '@/store/useSelectedGroupsTrail'
import { useSelectedSquadsTrail } from '@/store/useSelectedSquadsTrail'
import { useStepOneSaved } from '@/store/useStepOneSaved'
import { convertDateToCalendar } from '@/utils/convert-date'

import { useStudyPlanQueries } from '../contexts/StudyPlanQueriesContext'
import { durationOptions } from '../validations'
import { FieldLayout } from './components/fieldLayout'

export function Step4() {
  const { data: step1 } = useStepOneSaved()
  const { coursesSelected } = useSelectedCoursesTrail()
  const { allSquads, excludedSquads, squadsSelected } = useSelectedSquadsTrail()
  const { groupsSelected, allGroups, excludedGroups } = useSelectedGroupsTrail()
  const { collaboratorsSelected, allCollaborators, excludedCollaborators } =
    useSelectedCollaboratorsTrail()

  const { useCollaboratorsQuery, useTeamsQuery, useGroupsQuery } =
    useStudyPlanQueries()

  const foundDuration = durationOptions.find(
    (option) => option.value === (step1?.duration as string)
  )

  const convertedStartDate =
    step1?.startDate && convertDateToCalendar(step1?.startDate)
  const convertedEndDate =
    step1?.endDate && convertDateToCalendar(step1?.endDate)

  const { data: collaborators, isLoading: isLoadingCollaborators } =
    useCollaboratorsQuery({
      currentPage: 1,
      limit: 10,
    })

  const { data: teams, isLoading: isLoadingTeams } = useTeamsQuery({
    currentPage: 1,
    limit: 10,
  })

  const { data: groups, isLoading: isLoadingGroups } = useGroupsQuery({
    currentPage: 1,
    limit: 10,
  })

  const isLoading = isLoadingCollaborators || isLoadingTeams || isLoadingGroups

  const totalCollaborators = collaborators?.users.total as number
  const totalTeams = teams?.companySquads.total as number
  const totalGroups = groups?.companyGroups.total as number

  const totalCollaboratorsSelected = allCollaborators
    ? totalCollaborators - excludedCollaborators.length
    : collaboratorsSelected.length

  const totalGroupsSelected = allGroups
    ? totalGroups - excludedGroups.length
    : groupsSelected.length

  const totalSquadsSelected = allSquads
    ? totalTeams - excludedSquads.length
    : squadsSelected.length

  return (
    <div className="space-y-8 rounded-lg bg-ctx-layout-body p-4 lg:p-8">
      <h1 className="flex items-center gap-2 text-ctx-content-title ts-heading-sm">
        <HiOutlineClipboardDocumentCheck />
        Resumo
      </h1>

      <section className="space-y-8 rounded-xl">
        <FieldLayout defaultValue={step1?.name} title="Nome da trilha" />
        {step1?.description && (
          <FieldLayout
            title="Descrição da trilha"
            defaultValue={step1?.description}
          />
        )}
        {step1?.isMandatory && (
          <FieldLayout
            title="Prazo"
            defaultValue={
              foundDuration?.value === '4' // it means custom dates
                ? `${convertedStartDate} até ${convertedEndDate}`
                : (foundDuration?.label as string)
            }
          />
        )}
        <section className="space-y-2 rounded-xl border border-ctx-layout-border p-4">
          <h2 className="text-ctx-content-title ts-heading-xxs">
            Participantes
          </h2>
          <ScrollArea className="h-24 w-full rounded-lg border border-border bg-ctx-interactive-secondary p-2 px-4 text-ctx-content-base">
            {!isLoading && (
              <ul className="space-y-1.5 ts-heading-xxs">
                <li className="font-extrabold">
                  Colaboradores: {totalCollaboratorsSelected}
                </li>

                <li className="font-extrabold">
                  Equipes: {totalSquadsSelected}
                </li>

                <li className="font-extrabold">
                  Grupos: {totalGroupsSelected}
                </li>
              </ul>
            )}
          </ScrollArea>
        </section>
        <section className="space-y-2 rounded-xl border border-ctx-layout-border p-4">
          <h2 className="text-ctx-content-title ts-heading-xxs">Cursos</h2>
          <ScrollArea className="h-22 w-full rounded-lg border border-border bg-ctx-interactive-secondary p-2 px-4 text-ctx-content-base">
            <ul className="space-y-1.5 ts-heading-xxs">
              <li className="font-extrabold">
                Cursos: {coursesSelected.length}
              </li>
            </ul>
          </ScrollArea>
        </section>
      </section>
    </div>
  )
}
