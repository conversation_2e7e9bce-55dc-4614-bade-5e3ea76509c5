'use client'

import { HiOutlineClipboardDocumentCheck } from 'react-icons/hi2'
import Cookies from 'universal-cookie'

import { ScrollArea } from '@/components/ui/scroll-area'
import { useSelectedCollaboratorsTrail } from '@/store/useSelectedCollaboratorsTrail'
import { useSelectedCoursesTrail } from '@/store/useSelectedCoursesTrail'
import { useSelectedGroupsTrail } from '@/store/useSelectedGroupsTrail'
import { useSelectedSquadsTrail } from '@/store/useSelectedSquadsTrail'
import { convertDateToCalendar } from '@/utils/convert-date'

import { durationOptions } from '../validations'
import { FieldLayout } from './components/fieldLayout'

const totalCollaborators = 10
const totalGroups = 10
const totalSquads = 10

export function Step4() {
  const cookies = new Cookies(null, { path: '/' })
  const step1 = cookies.get('step1')

  const { coursesSelected } = useSelectedCoursesTrail()
  const { allSquads, excludedSquads, squadsSelected } = useSelectedSquadsTrail()
  const { groupsSelected, allGroups, excludedGroups } = useSelectedGroupsTrail()
  const { collaboratorsSelected, allCollaborators, excludedCollaborators } =
    useSelectedCollaboratorsTrail()

  const totalCollaboratorsSelected = allCollaborators
    ? totalCollaborators - excludedCollaborators.length
    : collaboratorsSelected.length

  const totalGroupsSelected = allGroups
    ? totalGroups - excludedGroups.length
    : groupsSelected.length

  const totalSquadsSelected = allSquads
    ? totalSquads - excludedSquads.length
    : squadsSelected.length

  const foundDuration = durationOptions.find(
    (option) => option.value === step1.duration
  )

  const convertedStartDate = convertDateToCalendar(step1.startDate)
  const convertedEndDate = convertDateToCalendar(step1.endDate)

  return (
    <div className="space-y-8 rounded-lg bg-ctx-layout-body p-4 lg:p-8">
      <h1 className="flex items-center gap-2 text-ctx-content-title ts-heading-sm">
        <HiOutlineClipboardDocumentCheck />
        Resumo
      </h1>

      <section className="space-y-8 rounded-xl">
        <FieldLayout defaultValue={step1?.name} title="Nome da trilha" />
        {step1?.description && (
          <FieldLayout
            title="Descrição da trilha"
            defaultValue={step1?.description}
          />
        )}
        <FieldLayout
          title="Prazo"
          defaultValue={
            foundDuration?.value === '4'
              ? `${convertedStartDate} até ${convertedEndDate}`
              : (foundDuration?.label as string)
          }
        />
        <section className="space-y-2 rounded-xl border border-ctx-layout-border p-4">
          <ScrollArea className="h-24 w-full rounded-lg border border-border bg-ctx-interactive-secondary p-2 px-4 text-ctx-content-base">
            <ul className="space-y-1.5 ts-heading-xxs">
              <li className="font-extrabold">
                Colaboradores: {totalCollaboratorsSelected}
              </li>

              <li className="font-extrabold">Equipes: {totalSquadsSelected}</li>

              <li className="font-extrabold">Grupos: {totalGroupsSelected}</li>
            </ul>
          </ScrollArea>
        </section>
        <section className="space-y-2 rounded-xl border border-ctx-layout-border p-4">
          <ScrollArea className="h-24 w-full rounded-lg border border-border bg-ctx-interactive-secondary p-2 px-4 text-ctx-content-base">
            <ul className="space-y-1.5 ts-heading-xxs">
              <li className="font-extrabold">
                Cursos: {coursesSelected.length}
              </li>
            </ul>
          </ScrollArea>
        </section>
      </section>
    </div>
  )
}
