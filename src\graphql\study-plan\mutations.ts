import { gql } from 'graphql-request'

export const CREATE_STUDY_PLAN = gql`
  mutation CreateStudyPlan(
    $name: String!
    $description: String!
    $courses: [StudyPlanCoursePivot!]!
    $company_id: Float!
    $squad_ids: [Float!]
    $user_ids: [Float!]
    $is_pdi: Boolean
    $is_for_all_users: Boolean
    $is_for_all_squads: Boolean
    $is_for_all_courses: Boolean
  ) {
    createStudyPlan(
      data: {
        name: $name
        description: $description
        courses: $courses
        company_id: $company_id
        squad_ids: $squad_ids
        user_ids: $user_ids
        is_pdi: $is_pdi
        is_for_all_users: $is_for_all_users
        is_for_all_squads: $is_for_all_squads
        is_for_all_courses: $is_for_all_courses
      }
    ) {
      id
    }
  }
`

export const CREATE_STUDY_PLAN_V2 = gql`
  mutation CreateStudyPlanV2(
    $name: String!
    $description: String!
    $courses: [StudyPlanCoursePivot!]!
    $company_id: Float!
    $is_pdi: Boolean
    $ai_generated: Boolean
    $select_all_user_ids: Boolean
    $user_ids_selected: [Float!]
    $user_ids_deselected: [Float!]
    $select_all_squad_ids: Boolean
    $squad_ids_selected: [Float!]
    $squad_ids_deselected: [Float!]
    $select_all_group_ids: Boolean
    $group_ids_selected: [Float!]
    $group_ids_deselected: [Float!]
    $end_date: DateTime
  ) {
    createStudyPlanV2(
      data: {
        name: $name
        description: $description
        courses: $courses
        company_id: $company_id
        is_pdi: $is_pdi
        ai_generated: $ai_generated
        end_date: $end_date
        select_all_user_ids: $select_all_user_ids
        user_ids_selected: $user_ids_selected
        user_ids_deselected: $user_ids_deselected
        select_all_squad_ids: $select_all_squad_ids
        squad_ids_selected: $squad_ids_selected
        squad_ids_deselected: $squad_ids_deselected
        select_all_group_ids: $select_all_group_ids
        group_ids_selected: $group_ids_selected
        group_ids_deselected: $group_ids_deselected
      }
    ) {
      id
    }
  }
`

export const DELETE_STUDY_PLAN = gql`
  mutation deleteStudyPlan($id: Float!) {
    deleteStudyPlan(id: $id)
  }
`

export const UPDATE_TEAMS_AND_USER_STUDY_PLAN = gql`
  mutation UpdatedTeamsAndUserPlanStudy(
    $id: Float!
    $squad_ids: [Float!]!
    $user_ids: [Float!]!
    $notify: Boolean!
    $is_for_all_users: Boolean
    $is_for_all_squads: Boolean
    $is_for_all_courses: Boolean
  ) {
    updateStudyPlan(
      data: {
        id: $id
        squad_ids: $squad_ids
        user_ids: $user_ids
        notify: $notify
        is_for_all_users: $is_for_all_users
        is_for_all_squads: $is_for_all_squads
        is_for_all_courses: $is_for_all_courses
      }
    ) {
      id
    }
  }
`
