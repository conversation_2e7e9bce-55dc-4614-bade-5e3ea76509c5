import { create } from 'zustand'

type SquadSelected = {
  id: number
  name: string
}

type SquadsStore = {
  squadsSelected: SquadSelected[]
  allSquads: boolean
  excludedSquads: number[]
  toggleSquad: (squad: SquadSelected) => void
  isSquadSelecionado: (id: number) => boolean
  setSquadsSelected: (squads: SquadSelected[]) => void
  checkAllSquads: () => void
}

export const useSelectedSquadsTrail = create<SquadsStore>((set, get) => ({
  squadsSelected: [],
  allSquads: false,
  excludedSquads: [],

  toggleSquad: (squad) => {
    const { allSquads, excludedSquads, squadsSelected } = get()

    if (allSquads) {
      if (excludedSquads.includes(squad.id)) {
        set({
          excludedSquads: excludedSquads.filter((id) => id !== squad.id),
        })
      } else {
        set({
          excludedSquads: [...excludedSquads, squad.id],
        })
      }
    } else {
      const exists = squadsSelected.some((s) => s.id === squad.id)
      if (exists) {
        set({
          squadsSelected: squadsSelected.filter((s) => s.id !== squad.id),
        })
      } else {
        set({
          squadsSelected: [...squadsSelected, squad],
        })
      }
    }
  },

  isSquadSelecionado: (id) => {
    const { allSquads, excludedSquads, squadsSelected } = get()

    if (allSquads) {
      return !excludedSquads.includes(id)
    }

    return squadsSelected.some((s) => s.id === id)
  },

  setSquadsSelected: (squads) => set({ squadsSelected: squads }),

  checkAllSquads: () =>
    set((state) => ({
      allSquads: !state.allSquads,
      excludedSquads: [],
    })),
}))
