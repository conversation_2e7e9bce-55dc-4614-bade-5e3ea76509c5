import { Skeleton } from '@/components/ui/skeleton'

export function ChatHistorySkeleton() {
  return (
    <div className="flex flex-col bg-ctx-layout-body px-4 md:h-[calc(100vh-235px)]">
      <div className="flex flex-col space-y-6">
        {Array.from({ length: 10 }).map((_, i) => (
          <div key={i} className="space-y-4">
            <Skeleton className="h-4 w-14" />
            <div className="flex flex-col space-y-4 px-6">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
