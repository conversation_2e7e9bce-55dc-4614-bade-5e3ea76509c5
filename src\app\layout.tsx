import '@ads/components-react/dist/Solution-styles.css'
import './globals.css'

import type { Metadata } from 'next'
import { Archivo, Inter } from 'next/font/google'

import { Providers } from '@/providers'

import { ThemeProvider } from './theme-provider'

const inter = Inter({
  subsets: ['latin'],
})

const archivo = Archivo({
  subsets: ['latin'],
  weight: ['400', '500', '700'],
})

export const metadata: Metadata = {
  title: 'Chat | Solution',
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html
      lang="pt-BR"
      suppressHydrationWarning
      className={`${inter.className} ${archivo.className}`}
    >
      <body className="bg-ctx-layout-body">
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <Providers>{children}</Providers>
        </ThemeProvider>
      </body>
    </html>
  )
}
