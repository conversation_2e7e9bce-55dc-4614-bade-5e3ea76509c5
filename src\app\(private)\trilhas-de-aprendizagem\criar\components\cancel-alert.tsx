import { Button } from '@ads/components-react'
import { useRouter } from 'next/navigation'
import Cookies from 'universal-cookie'

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import { Separator } from '@/components/ui/separator'

export function CancelNewStudyPlanAlert() {
  const router = useRouter()
  const cookies = new Cookies(null, { path: '/' })

  const handleCancel = () => {
    cookies.remove('step1')
    router.push('/trilhas-de-aprendizagem')
  }
  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button
          type="button"
          hierarchy="tertiary"
          className="flex-1 sm:flex-none"
        >
          Cancelar
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Tem certeza que deseja cancelar?</AlertDialogTitle>
          <AlertDialogDescription>
            Ao cancelar, todas as informações preenchidas até agora serão
            perdidas e não poderão ser recuperadas.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <Separator />
        <AlertDialogFooter className="h-20 flex-col gap-2 sm:h-full sm:flex-row">
          <AlertDialogCancel asChild>
            <Button hierarchy="secondary" className="w-full flex-1">
              Cancelar
            </Button>
          </AlertDialogCancel>
          <AlertDialogAction asChild>
            <Button
              hierarchy="danger"
              onClick={handleCancel}
              className="w-full flex-1"
            >
              Cancelar criação
            </Button>
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
