import { Button } from '@ads/components-react'
import { useRouter } from 'next/navigation'
import Cookies from 'universal-cookie'

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import { Separator } from '@/components/ui/separator'

export function CancelNewStudyPlanAlert() {
  const router = useRouter()
  const cookies = new Cookies(null, { path: '/' })

  const handleCancel = () => {
    cookies.remove('step1')
    router.push('/trilhas-de-aprendizagem')
  }
  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button type="button" hierarchy="tertiary" className="mr-2">
          Cancelar
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Tem certeza que deseja cancelar?</AlertDialogTitle>
          <AlertDialogDescription>
            Ao cancelar, todas as informações preenchidas até agora serão
            perdidas e não poderão ser recuperadas.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <Separator />
        <AlertDialogFooter>
          <AlertDialogCancel asChild>
            <Button hierarchy="secondary">Cancelar</Button>
          </AlertDialogCancel>
          <AlertDialogAction asChild>
            <Button hierarchy="danger" onClick={handleCancel}>
              Cancelar criação
            </Button>
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
