import {
  Dropdown,
  DropdownItem,
  DropdownItem<PERSON>ontent,
  I<PERSON><PERSON>utton,
  useAlert,
} from '@ads/components-react'
import { Edit, EllipsisVertical } from 'lucide-react'
import Link from 'next/link'
import { useMutation, useQueryClient } from 'react-query'

import { DeleteTeamsAlert } from '@/components/alerts-dialogs/delete-teams'
import { Separator } from '@/components/ui/separator'
import { GetTeamsQuery } from '@/graphql/generated/graphql'
import { deleteTeam } from '@/http/teams/delete-team'

type TeamDropdownOptionsProps = {
  team: GetTeamsQuery['companySquads']['data'][number]
}

export function TeamDropdownOptions({ team }: TeamDropdownOptionsProps) {
  const queryClient = useQueryClient()
  const { alert } = useAlert()

  const { mutate: onDeleteMutation } = useMutation({
    mutationFn: () => deleteTeam(team.id),
    onSuccess: () => {
      queryClient.refetchQueries(['getTeamsGql'], {
        active: true,
        exact: false,
      })
      alert({
        title: 'Equipe excluída com sucesso!',
        description: `A equipe ${team.title} foi excluída com sucesso.`,
        alertType: 'success',
      })
    },
    onError: () => {
      alert({
        title: 'Erro ao excluir equipe',
        description: `Ocorreu um erro ao excluir a equipe ${team.title}. Tente novamente mais tarde.`,
        alertType: 'danger',
      })
    },
  })

  return (
    <>
      <Dropdown
        className="w-[216px]"
        trigger={
          <div className="flex w-full justify-end">
            <IconButton
              ariaLabel="Editar"
              icon={EllipsisVertical}
              hierarchy="tertiary"
              className="!p-0"
            />
          </div>
        }
      >
        <Link href={`/equipes/${team.id}`} className="w-full">
          <DropdownItem>
            <DropdownItemContent leadingIcon={Edit}>Editar</DropdownItemContent>
          </DropdownItem>
        </Link>
        <Separator />

        <DeleteTeamsAlert teamsMutation={onDeleteMutation} />
      </Dropdown>
    </>
  )
}
