import { SelectInput, SelectInputItem } from '@ads/components-react'

type SelectAdsProps = {
  options: { value: string; label: string }[]
  errorMessage?: string
} & React.ComponentProps<typeof SelectInput>

export function SelectAds({ options, errorMessage, ...props }: SelectAdsProps) {
  const hasError = Boolean(errorMessage)

  return (
    <div className="relative flex w-full flex-col gap-1.5">
      <SelectInput
        {...props}
        custom={{ trigger: 'h-10', content: 'z-40' }}
        hasError={hasError}
      >
        {options.map((option) => (
          <SelectInputItem key={option.value} value={String(option.value)}>
            {option.label}
          </SelectInputItem>
        ))}
      </SelectInput>
      {errorMessage && (
        <span className="text-red-500 ts-subtitle-xxxs">{errorMessage}</span>
      )}
    </div>
  )
}
