import { create } from 'zustand'
import { persist } from 'zustand/middleware'

type StepOneData = {
  name: string
  description: string
  isMandatory: boolean
  duration: number
  startDate: Date
  endDate: Date
}

type StepOneSavedProps = {
  data: StepOneData
  setData: (data: StepOneData) => void
  clearData: () => void
}

const defaultValue: StepOneData = {
  name: '',
  description: '',
  isMandatory: false,
  duration: 0,
  startDate: new Date(),
  endDate: new Date(),
}

export const useStepOneSaved = create<StepOneSavedProps>()(
  persist(
    (set) => ({
      data: defaultValue,
      setData: (data) => set({ data }),
      clearData: () => set({ data: defaultValue }),
    }),
    {
      name: 'step1',
    }
  )
)
