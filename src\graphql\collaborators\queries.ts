import { gql } from 'graphql-request'

export const GET_COLLABORATORS = gql`
  query GetCollaborators(
    $page: Float!
    $limit: Float!
    $company_id: Float!
    $enrollment_id: Float!
    $orderBy: EListOrderBy!
    $order: EListOrder!
    $q: String
  ) {
    users(
      page: $page
      limit: $limit
      company_id: $company_id
      orderBy: $orderBy
      order: $order
      enrollment_id: $enrollment_id
      q: $q
    ) {
      total
      perPage
      data {
        id
        name
        email
        updated_at
        seniority
        position
        enrollments {
          id
        }
        roles {
          name
        }
        metadata {
          company_squad {
            title
          }
        }
      }
    }
  }
`

export const GET_LICENSES_COUNT = gql`
  query GetLicensesCount($enrollment_id: Int!) {
    enrollment(id: $enrollment_id) {
      b2b_metadata {
        licenses_count
        employees_count(employee_status: ACTIVE)
      }
    }
  }
`
