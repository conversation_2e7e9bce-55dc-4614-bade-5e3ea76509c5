import { EPosition } from '@/enum/position'
import { ESeniority } from '@/enum/seniority'
import { Avatar } from '@/model/avatar'
import api from '@/services/api'

export interface CreateUserProps {
  data: {
    name: string
    email: string
    company_id: number
    redirect_url?: string
    enrollment_id?: number
    squad_id?: number
    avatar?: Avatar
    role_ids?: number[]
    groups_ids?: number[]
    position?: EPosition | null
    seniority?: ESeniority | null
    birthdate?: string
    admitted_at?: string
  }
}

export async function createUser({ data }: CreateUserProps) {
  const { data: response } = await api.post('/b2b-users', { ...data })

  return response
}
