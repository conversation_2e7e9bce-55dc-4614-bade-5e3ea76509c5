'use client'

import { But<PERSON>, useAlert } from '@ads/components-react'
import { ArrowDownIcon } from 'lucide-react'
import { useParams } from 'next/navigation'
import { useEffect, useRef, useState } from 'react'
import { useMutation, useQuery, useQueryClient } from 'react-query'

import NotFound from '@/app/not-found'
import { CourseCard } from '@/components/course-card'
import { InputContextMessage } from '@/components/Input-context-message'
import { ChatPageSkeleton } from '@/components/loaders/skeletons/chat-page'
import { TypingIndicator } from '@/components/loaders/typing-indicator'
import { ResponseAI } from '@/components/response-ai'
import { InputChat } from '@/components/ui/input-chat'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import { getChatMessageAI } from '@/http/chat-ai/get-chat-ai-message'
import { patchMessageAi } from '@/http/chat-ai/patch-message-ai'

export function ChatAI() {
  const { alert } = useAlert()
  const params = useParams()
  const chatId = params.id
  const queryClient = useQueryClient()

  const [newMessage, setNewMessage] = useState('')
  const [visibleCoursesPerMessage, setVisibleCoursesPerMessage] = useState<
    Record<string, number>
  >({})
  const scrollAreaRef = useRef<HTMLDivElement>(null)

  const CARDS_PER_PAGE = 4

  const {
    data: messages,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ['chat', chatId],
    queryFn: () => getChatMessageAI(chatId as string),
    enabled: !!chatId,
    refetchOnWindowFocus: false,
    staleTime: 0,
    cacheTime: 5 * 60 * 1000, // 5 minutos
  })

  const { mutate: sendMessage, isLoading: isSendingMessage } = useMutation({
    mutationFn: (context: string) => patchMessageAi(chatId as string, context),
    onSuccess: async () => {
      await queryClient.invalidateQueries(['chat', chatId])

      setTimeout(async () => {
        await refetch()

        if (scrollAreaRef.current) {
          const scrollContainer = scrollAreaRef.current.querySelector(
            '[data-radix-scroll-area-viewport]'
          )
          if (scrollContainer) {
            scrollContainer.scrollTop = scrollContainer.scrollHeight
          }
        }
      }, 100)
    },
    onError: () => {
      alert({
        title: 'Erro ao enviar mensagem',
        description:
          'Ocorreu um erro ao enviar sua mensagem. Tente novamente mais tarde.',
        alertType: 'danger',
      })
    },
  })

  function handleSendMessage() {
    if (!newMessage.trim() || isSendingMessage) return

    const messageToSend = newMessage.trim()

    setNewMessage('')

    sendMessage(messageToSend)

    setTimeout(() => {
      scrollToBottom()
    }, 50)
  }

  function handleKeyDown(e: React.KeyboardEvent<HTMLTextAreaElement>) {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  function handleLoadMoreCourses(messageId: string) {
    setVisibleCoursesPerMessage((prev) => {
      const updated = {
        ...prev,
        [messageId]: (prev[messageId] || CARDS_PER_PAGE) + CARDS_PER_PAGE,
      }

      setTimeout(() => {
        scrollToBottom()
      }, 50)

      return updated
    })
  }

  function getVisibleCoursesCount(messageId: string, totalCourses: number) {
    const visibleCount = visibleCoursesPerMessage[messageId] || CARDS_PER_PAGE
    return Math.min(visibleCount, totalCourses)
  }

  function hasMoreCourses(messageId: string, totalCourses: number) {
    const visibleCount = visibleCoursesPerMessage[messageId] || CARDS_PER_PAGE
    return visibleCount < totalCourses
  }

  function scrollToBottom() {
    if (scrollAreaRef.current) {
      const scrollContainer = scrollAreaRef.current.querySelector(
        '[data-radix-scroll-area-viewport]'
      )
      if (scrollContainer) {
        scrollContainer.scrollTo({
          top: scrollContainer.scrollHeight,
          behavior: 'smooth',
        })
      }
    }
  }

  useEffect(() => {
    scrollToBottom()
  }, [scrollAreaRef])

  useEffect(() => {
    if (messages) {
      const initialState: Record<string, number> = {}
      messages.forEach((message) => {
        if (
          message.ai_response.courses &&
          message.ai_response.courses.length > 0
        ) {
          initialState[message.id] = CARDS_PER_PAGE
        }
      })
      setVisibleCoursesPerMessage((prev) => ({ ...initialState, ...prev }))
    }
  }, [messages])

  if (isLoading) {
    return (
      <div className="flex h-[calc(100vh-125px)] flex-col overflow-hidden bg-ctx-layout-body md:h-[calc(100vh-130px)]">
        <ChatPageSkeleton />
      </div>
    )
  }

  if (!messages || messages.length === 0) {
    return <NotFound />
  }

  const sortedMessages = [...messages]

  return (
    <div className="flex h-[calc(100vh-125px)] flex-col overflow-hidden md:h-[calc(100vh-130px)]">
      <div className="min-h-0 flex-1 overflow-hidden">
        <ScrollArea className="h-full" ref={scrollAreaRef}>
          <div className="mx-auto w-full max-w-[58rem] space-y-6 p-4 md:px-4">
            {sortedMessages.map((message) => {
              const aiResponse = message.ai_response
              const courses = aiResponse.courses || []
              const visibleCoursesCount = getVisibleCoursesCount(
                message.id,
                courses.length
              )
              const visibleCourses = courses.slice(0, visibleCoursesCount)
              const showLoadMore = hasMoreCourses(message.id, courses.length)

              return (
                <div key={message.id} className="space-y-4">
                  <InputContextMessage content={message.user_input} />

                  <ResponseAI
                    content={
                      aiResponse.content
                        ? aiResponse.content
                        : `${aiResponse.description || ''} ${
                            courses.length > 0
                              ? `<strong>Confira os ${aiResponse.count} cursos sugeridos:</strong>`
                              : ''
                          }`
                    }
                  />

                  {courses.length > 0 && (
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                        {visibleCourses.map((course, index) => (
                          <CourseCard
                            key={course.id}
                            id={String(course.id)}
                            index={index + 1}
                            title={course.title}
                            imageUrl={course.image_url}
                            imageAlt={`Imagem do curso ${course.title}`}
                            justification={course.justification}
                          />
                        ))}
                      </div>

                      {showLoadMore && (
                        <div className="flex justify-center pt-2">
                          <Button
                            hierarchy="secondary"
                            trailingIcon={ArrowDownIcon}
                            onClick={() => handleLoadMoreCourses(message.id)}
                          >
                            Carregar mais sugestões
                          </Button>

                          <div ref={scrollAreaRef} />
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )
            })}

            {isSendingMessage && <TypingIndicator />}
          </div>
        </ScrollArea>
      </div>

      <div className="">
        <Separator className="my-2" />
        <div className="mx-auto w-full max-w-[58rem] px-2 md:px-4">
          <InputChat
            placeholder="Continue a conversa... Pergunte sobre os cursos, solicite mais opções ou esclareça dúvidas"
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            onKeyDown={handleKeyDown}
            onClickSendMessage={handleSendMessage}
            disabled={isSendingMessage}
            disabledButtonMessage={isSendingMessage || !newMessage.trim()}
          />
        </div>
      </div>
    </div>
  )
}
