'use client'

import { <PERSON><PERSON>, Checkbox, useAlert } from '@ads/components-react'
import { ArrowDownIcon } from 'lucide-react'
import { useParams } from 'next/navigation'
import { useCallback, useState } from 'react'
import { useInfiniteQuery, useMutation, useQuery } from 'react-query'
import Cookies from 'universal-cookie'

import NotFound from '@/app/not-found'
import { CardCourse } from '@/components/card-course'
import { InputContextMessage } from '@/components/Input-context-message'
import { ChatPageSkeleton } from '@/components/loaders/skeletons/chat-page'
import { ResponseAI } from '@/components/response-ai'
import { InputChat } from '@/components/ui/input-chat'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import { useAuth } from '@/contexts/AuthContext'
import {
  CreateStudyPlanMutationVariables,
  type UpdatedTeamsAndUserPlanStudyMutationVariables,
} from '@/graphql/generated/graphql'
import { getChatMessageAI } from '@/http/chat-ai/get-chat-ai-message'
import {
  createNewStudyPlan,
  updateTeamsUsersStudyPlan,
} from '@/http/study-plan'
import { getB2bUsers } from '@/http/user'

export interface Course {
  id: number
  title: string
  justification: string
  description: string
  image_url: string
}

interface ChatMessage {
  id: string
  type: 'user' | 'ai'
  content: string
  timestamp: Date
  metadata?: {
    courses?: Course[]
    selectedCourses?: string[]
    studyPlanCreated?: boolean
    showCoursesSelection?: boolean
    isNamingStep?: boolean
    showAssignButton?: boolean
  }
}

export function ChatAI() {
  const { alert } = useAlert()
  const { user } = useAuth()
  const params = useParams()
  const chatId = params.id

  const cookies = new Cookies(null, { path: '/' })
  const companyId = cookies.get('b2bCompanyId')
  const enrollmentId = cookies.get('b2bEnrollmentId')

  const [selectedIds, setSelectedIds] = useState<string[]>([])
  const [showInputStudyPlanName, setShowInputStudyPlanName] = useState(false)
  const [studyPlanName, setStudyPlanName] = useState('')
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([])
  const [assignClicked, setAssignClicked] = useState(false)
  const [selectedUserIds, setSelectedUserIds] = useState<number[]>([])
  const [studyPlanId, setStudyPlanId] = useState<null | number>(null)

  const { data: dataGetMessageAI, isLoading } = useQuery({
    queryKey: ['chat', chatId],
    queryFn: () => getChatMessageAI(chatId as string),
    onSuccess: (data) => {
      if (data && data.length > 0 && chatMessages.length === 0) {
        const initialMessages: ChatMessage[] = [
          {
            id: `user-${Date.now()}`,
            type: 'user',
            content: data[0]?.user_input ?? '',
            timestamp: new Date(),
          },
          {
            id: `ai-${Date.now()}`,
            type: 'ai',
            content:
              data[0].ai_response.content ||
              `${data[0].ai_response.description}<strong>Confira os ${data[0].ai_response.count} sugeridos:</strong>`,
            timestamp: new Date(),
            metadata: {
              courses: data[0].ai_response.courses,
              showCoursesSelection: true,
            },
          },
        ]
        setChatMessages(initialMessages)
      }
    },
  })

  const {
    mutate: updatedStudyPlanMutation,
    isLoading: isUpdatingStudyPlan,
    isSuccess: isUpdatingStudyPlanSuccess,
  } = useMutation({
    mutationFn: (data: UpdatedTeamsAndUserPlanStudyMutationVariables) =>
      updateTeamsUsersStudyPlan(data),
    onSuccess: () => {
      alert({
        title: 'Usuários atribuídos com sucesso!',
        description: `Os usuários foram atribuídos à trilha  com sucesso.`,
        alertType: 'success',
      })

      addMessageToHistory({
        type: 'user',
        content: 'Confirmar atribuição',
      })
    },
    onError: () => {
      alert({
        title: 'Erro ao atribuir usuários',
        description:
          'Ocorreu um erro ao atribuir os usuários à trilha. Tente novamente mais tarde.',
        alertType: 'danger',
      })
    },
  })

  const { mutate: createStudyPlanMutation, isLoading: isCreatingStudyPlan } =
    useMutation({
      mutationFn: (data: CreateStudyPlanMutationVariables) =>
        createNewStudyPlan(data),
      onSuccess: (response, data) => {
        alert({
          title: 'Trilha de aprendizado criada com sucesso!',
          description: `A trilha ${data.name} foi criada com sucesso.`,
          alertType: 'success',
        })

        addMessageToHistory({
          type: 'ai',
          content: `A trilha "${data.name}" foi criada com sucesso com ${selectedIds.length} curso(s) selecionado(s). Posso ajudar você com algo mais?`,
          metadata: { studyPlanCreated: true },
        })

        setStudyPlanId(response.createStudyPlan.id)
      },
      onError: () => {
        alert({
          title: 'Erro ao criar trilha de aprendizado',
          description:
            'Ocorreu um erro ao criar a trilha de aprendizado. Tente novamente mais tarde.',
          alertType: 'danger',
        })

        addMessageToHistory({
          type: 'ai',
          content: 'Ocorreu um erro ao criar a trilha. Vamos tentar novamente?',
        })
      },
    })

  const {
    data: users,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useInfiniteQuery({
    queryKey: ['getb2bUsersGql'],
    queryFn: ({ pageParam = 1 }) =>
      getB2bUsers({
        page: pageParam,
        limit: 10,
        orderBy: 'NAME',
        company_id: companyId,
        enrollment_id: enrollmentId,
        q: undefined,
      }),

    getNextPageParam: (response, allPagesData) => {
      const nextPage = allPagesData.length + 1
      return nextPage <= Math.ceil(response.users.total / 10)
        ? nextPage
        : undefined
    },
    keepPreviousData: false,
    staleTime: 60 * 1000 * 10, // 10 minutos
    cacheTime: 0,
  })

  const flattedData = users?.pages?.flatMap((page) => page.users.data) ?? []

  const loadMore = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage()
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage])

  function addMessageToHistory(message: Omit<ChatMessage, 'id' | 'timestamp'>) {
    const newMessage: ChatMessage = {
      ...message,
      id: `${message.type}-${Date.now()}`,
      timestamp: new Date(),
    }
    setChatMessages((prev) => [...prev, newMessage])
  }

  function handleCardChange(id: string, checked: boolean) {
    setSelectedIds((prev) => {
      const set = new Set(prev)
      if (checked) set.add(id)
      else set.delete(id)
      return Array.from(set)
    })
  }

  function handleUserCheckChange(userId: number, checked: boolean) {
    setSelectedUserIds((prev) => {
      const set = new Set(prev)
      if (checked) set.add(userId)
      else set.delete(userId)
      return Array.from(set)
    })
  }

  function handleStartStudyPlanCreation() {
    setShowInputStudyPlanName(true)

    addMessageToHistory({
      type: 'user',
      content: 'Criar trilha com os cursos selecionados',
    })

    addMessageToHistory({
      type: 'ai',
      content: 'Vamos criar uma trilha nova! Como você gostaria de chamá-la?',
      metadata: {
        isNamingStep: true,
        selectedCourses: selectedIds,
      },
    })
  }

  function handleAssignCollaborators() {
    setAssignClicked(true)
  }

  function handleStudyPlanNameSubmit(name: string) {
    addMessageToHistory({
      type: 'user',
      content: `Nome da trilha: "${name}"`,
    })

    createStudyPlanMutation({
      name,
      description: '',
      courses: selectedIds.map((id, index) => ({
        course_id: Number(id),
        order: index,
      })),
      company_id: user?.metadata.company_id as number,
      squad_ids: [],
      user_ids: [],
      is_pdi: false,
      is_for_all_users: false,
      is_for_all_squads: false,
      is_for_all_courses: false,
    })

    setShowInputStudyPlanName(false)
  }

  function handleStudyPlanAssignment() {
    setShowInputStudyPlanName(true)
    if (selectedUserIds) {
      updatedStudyPlanMutation({
        id: studyPlanId as number,
        squad_ids: [],
        user_ids: selectedUserIds,
        notify: true,
        is_for_all_users: false,
        is_for_all_squads: false,
        is_for_all_courses: false,
      })
    }
  }

  function handleSendKeyDown(
    e: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>
  ) {
    if (e.key === 'Enter') {
      e.preventDefault()
      const value = (e.target as HTMLTextAreaElement).value
      if (value) {
        handleStudyPlanNameSubmit(value)
      }
    }
  }

  if (isLoading) return <ChatPageSkeleton />
  if (!dataGetMessageAI) return <NotFound />

  const studyPlanWasCreated = chatMessages.some(
    (msg) => msg.metadata?.studyPlanCreated
  )

  return (
    <div className="flex h-full flex-col overflow-hidden md:h-[calc(100vh-207px)]">
      <div className="min-h-0 flex-1 overflow-hidden">
        <ScrollArea className="h-full">
          <div className="mx-auto w-full max-w-[58rem] space-y-4 p-4 md:px-4">
            {chatMessages.map((message) => (
              <div key={message.id}>
                {message.type === 'user' ? (
                  <InputContextMessage content={message.content} />
                ) : (
                  <ResponseAI content={message.content} />
                )}

                {message.type === 'ai' &&
                  message.metadata?.courses &&
                  message.metadata?.showCoursesSelection && (
                    <>
                      <div className="mt-4 space-y-4">
                        {message.metadata.courses.map(
                          (course: Course, courseIndex: number) => (
                            <div key={course.id}>
                              <CardCourse
                                id={String(course.id)}
                                index={courseIndex + 1}
                                title={course.title}
                                imageUrl={course.image_url}
                                imageAlt={`Imagem do curso ${course.title}`}
                                justification={course.justification}
                                checked={selectedIds.includes(
                                  String(course.id)
                                )}
                                onChange={handleCardChange}
                              />
                            </div>
                          )
                        )}
                      </div>

                      {!studyPlanWasCreated && (
                        <div className="mt-4 flex flex-col gap-4">
                          <span className="text-ctx-content-base ts-paragraph-xs">
                            Selecione pelo menos 1 curso sugerido e clique em
                            uma opção abaixo:
                          </span>
                          <Button
                            onClick={handleStartStudyPlanCreation}
                            hierarchy="secondary"
                            disabled={
                              selectedIds.length === 0 || isCreatingStudyPlan
                            }
                            className={
                              showInputStudyPlanName
                                ? 'border-2 border-ctx-highlight-focus'
                                : ''
                            }
                          >
                            Criar trilha com os cursos selecionados
                          </Button>
                        </div>
                      )}
                    </>
                  )}

                {message.metadata?.studyPlanCreated && (
                  <div className="mt-4 space-y-4">
                    {assignClicked && (
                      <>
                        <InputContextMessage content="Atribuir a Colaborador(es)" />
                        <div className="mt-2 space-y-4">
                          <ResponseAI
                            content={`
                              <span>
                                Encontrei ${flattedData?.length ?? 0} Colaboradores: <br /> Deseja atribuir os
                                cursos para todos ou selecionar membros específicos?
                              </span>`}
                          />

                          <div className="space-y-4 rounded-2xl border border-ctx-layout-border p-4">
                            {flattedData?.map((user) => (
                              <div
                                key={user.id}
                                className="flex items-center space-x-2"
                              >
                                <Checkbox
                                  checked={selectedUserIds.includes(user.id)}
                                  onCheckedChange={(checked) =>
                                    handleUserCheckChange(
                                      user.id,
                                      Boolean(checked)
                                    )
                                  }
                                  disabled={false}
                                  size="md"
                                  className="dark:rounded-sm dark:bg-[#FFFFFFB8]"
                                />

                                <div>
                                  <span className="text-ctx-content-title ts-paragraph-xs">
                                    {user.name} {user.email}
                                  </span>
                                </div>
                              </div>
                            ))}

                            {hasNextPage && (
                              <div className="flex flex-col items-center pt-4">
                                <span
                                  onClick={loadMore}
                                  className="flex cursor-pointer flex-col items-center justify-center text-ctx-content-title ts-paragraph-xs"
                                >
                                  {isFetchingNextPage
                                    ? 'Carregando colaboradores...'
                                    : 'Carregar mais colaboradores'}
                                  <ArrowDownIcon className="flex h-4 w-4 animate-bounce items-center justify-center" />
                                </span>
                              </div>
                            )}
                          </div>

                          <Button
                            onClick={handleStudyPlanAssignment}
                            hierarchy="secondary"
                            disabled={
                              selectedUserIds.length === 0 ||
                              isUpdatingStudyPlan ||
                              isUpdatingStudyPlanSuccess
                            }
                            className={
                              showInputStudyPlanName
                                ? 'border-2 border-ctx-highlight-focus'
                                : ''
                            }
                          >
                            Confirmar atribuição
                          </Button>
                        </div>
                      </>
                    )}

                    {!assignClicked && (
                      <Button
                        onClick={handleAssignCollaborators}
                        hierarchy="secondary"
                        className={
                          assignClicked
                            ? 'border-2 border-ctx-highlight-focus'
                            : ''
                        }
                      >
                        Atribuir a Colaborador(es)
                      </Button>
                    )}
                  </div>
                )}
              </div>
            ))}

            {isCreatingStudyPlan && (
              <div className="text-center">
                <p className="text-sm text-gray-600">Criando trilha...</p>
              </div>
            )}
          </div>
        </ScrollArea>
      </div>

      <div className="flex-shrink-0">
        <Separator className="my-2 md:my-4" />
        <div className="mx-auto w-full max-w-[50rem] px-4 pb-4">
          {showInputStudyPlanName && (
            <InputChat
              placeholder="Digite o nome da trilha e pressione Enter..."
              onChange={(e) => setStudyPlanName(e.target.value)}
              onKeyDown={(e) => {
                handleSendKeyDown(e)
              }}
              onClickSendMessage={() => {
                handleStudyPlanNameSubmit(studyPlanName)
              }}
              disabled={isCreatingStudyPlan}
            />
          )}

          {!showInputStudyPlanName && (
            <InputChat
              placeholder="Descreva seu desafio, dúvida ou envie um arquivo."
              disabled={isCreatingStudyPlan}
            />
          )}
        </div>
      </div>
    </div>
  )
}
