'use client'

import { create<PERSON>ontext, <PERSON>actNode, useContext } from 'react'
import { useQuery } from 'react-query'
import Cookies from 'universal-cookie'

import { ESolutionContext } from '@/enum/solution-context'
import {
  EListOrder,
  EListOrderBy,
  GetCollaboratorsQuery,
  GetCoursesQuery,
  GetGroupsQuery,
  GetTeamsQuery,
} from '@/graphql/generated/graphql'
import { getCollaborators } from '@/http/collaborators/get-collaborators'
import { getCourses } from '@/http/courses'
import { getGroups } from '@/http/groups'
import { getTeams } from '@/http/teams/get-teams'

interface StudyPlanQueriesContextData {
  useCoursesQuery: (params: {
    searchValue?: string
    currentPage: number
    limit?: number
  }) => ReturnType<typeof useQuery<GetCoursesQuery>>

  useCollaboratorsQuery: (params: {
    searchValue?: string
    currentPage: number
    limit?: number
  }) => ReturnType<typeof useQuery<GetCollaboratorsQuery>>

  useTeamsQuery: (params: {
    searchValue?: string
    currentPage: number
    limit?: number
  }) => ReturnType<typeof useQuery<GetTeamsQuery>>

  useGroupsQuery: (params: {
    searchValue?: string
    currentPage: number
    limit?: number
  }) => ReturnType<typeof useQuery<GetGroupsQuery>>
}

const StudyPlanQueriesContext = createContext<StudyPlanQueriesContextData>(
  {} as StudyPlanQueriesContextData
)

interface StudyPlanQueriesProviderProps {
  children: ReactNode
}

export function StudyPlanQueriesProvider({
  children,
}: StudyPlanQueriesProviderProps) {
  const cookies = new Cookies(null, { path: '/' })
  const companyId = cookies.get('b2bCompanyId')
  const enrollmentId = Number(cookies.get('b2bEnrollmentId'))

  const useCoursesQuery = ({
    searchValue = '',
    currentPage = 1,
    limit = 10,
  }) => {
    return useQuery({
      queryKey: ['studyPlanCourses', searchValue, currentPage],
      queryFn: () =>
        getCourses({
          page: currentPage,
          limit,
          q: searchValue || undefined,
          all: false,
          available_at: ESolutionContext.PRO.toLowerCase() as ESolutionContext,
        }),
      refetchOnWindowFocus: false,
      staleTime: 1000 * 60 * 5,
    })
  }

  const useCollaboratorsQuery = ({
    searchValue = '',
    currentPage = 1,
    limit = 10,
  }) => {
    return useQuery({
      queryKey: [
        'getCollaboratorsGql',
        searchValue,
        currentPage,
        companyId,
        enrollmentId,
      ],
      queryFn: () =>
        getCollaborators({
          page: currentPage,
          limit,
          company_id: companyId,
          enrollment_id: enrollmentId,
          order: EListOrder.ASC,
          orderBy: EListOrderBy.NAME,
          q: searchValue,
        }),
      refetchOnWindowFocus: false,
      staleTime: 1000 * 60 * 5,
    })
  }

  const useTeamsQuery = ({ searchValue = '', currentPage = 1, limit = 10 }) => {
    return useQuery({
      queryKey: ['getTeamsGql', searchValue, currentPage, companyId],
      queryFn: () =>
        getTeams({
          title: searchValue,
          company_id: companyId,
          page: currentPage,
          limit,
        }),
      refetchOnWindowFocus: false,
      staleTime: 1000 * 60 * 5,
    })
  }

  const useGroupsQuery = ({
    searchValue = '',
    currentPage = 1,
    limit = 10,
  }) => {
    return useQuery({
      queryKey: ['getGroupsGql', searchValue, currentPage, companyId],
      queryFn: () =>
        getGroups({
          name: searchValue,
          company_id: companyId,
          page: currentPage,
          limit,
        }),
      refetchOnWindowFocus: false,
      staleTime: 1000 * 60 * 5,
    })
  }

  return (
    <StudyPlanQueriesContext.Provider
      value={{
        useCoursesQuery,
        useCollaboratorsQuery,
        useTeamsQuery,
        useGroupsQuery,
      }}
    >
      {children}
    </StudyPlanQueriesContext.Provider>
  )
}

export function useStudyPlanQueries() {
  const context = useContext(StudyPlanQueriesContext)

  if (!context) {
    throw new Error(
      'useStudyPlanQueries must be used within a StudyPlanQueriesProvider'
    )
  }

  return context
}
