'use client'

import { useRouter, useSearchParams } from 'next/navigation'
import { Suspense, useEffect, useRef, useState } from 'react'
import { ImperativePanelHandle } from 'react-resizable-panels'

import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from '@/components/ui/resizable'
import { useIsMobile } from '@/hooks/use-mobile'

import { ChatPanel } from './chat'
import { Trilhas } from './trilha'

function LearningPathsContent() {
  const isMobile = useIsMobile()
  const searchParams = useSearchParams()
  const router = useRouter()
  const chatIdFromQuery = searchParams.get('chatId')

  const [isExpanded, setIsExpanded] = useState(false)
  const [isClosed, setIsClosed] = useState(true)
  const [hasUserInteracted, setHasUserInteracted] = useState(false)
  const [isInitialized, setIsInitialized] = useState(false)

  const leftPanelRef = useRef<ImperativePanelHandle>(null)

  function handleExpandToggle() {
    setHasUserInteracted(true)
    if (!leftPanelRef.current) return

    if (isExpanded) {
      leftPanelRef.current.expand()
      return
    }

    leftPanelRef.current.collapse()
  }

  function handleLeftPanelCollapse() {
    setIsExpanded(true)

    if (isMobile) {
      setHasUserInteracted(false)
    }
  }

  function handleLeftPanelExpand() {
    setIsExpanded(false)

    if (isMobile) {
      setHasUserInteracted(false)
    }
  }

  function clearQueryParams() {
    const params = new URLSearchParams(searchParams.toString())
    params.delete('chatId')
    router.replace(
      `/trilhas-de-aprendizagem${params.toString() ? `?${params.toString()}` : ''}`
    )
  }

  function handleClose() {
    setIsClosed(true)

    clearQueryParams()
  }

  useEffect(() => {
    setIsInitialized(true)
  }, [])

  useEffect(() => {
    if (!isInitialized) return

    if ((!hasUserInteracted || isMobile) && leftPanelRef.current) {
      if (isMobile) {
        leftPanelRef.current.collapse()
        setIsExpanded(true)
      } else {
        leftPanelRef.current.expand()
        setIsExpanded(false)
      }
    }
  }, [isMobile, hasUserInteracted, isInitialized])

  useEffect(() => {
    if (isMobile && !isClosed) {
      setIsExpanded(true)
    }
  }, [isMobile, isClosed])

  if (isMobile) {
    return (
      <div className="flex h-full flex-col">
        {isClosed ? (
          <div className="flex-1">
            <Trilhas
              isExpanded={isExpanded}
              setIsExpanded={() => {
                setIsClosed(false)
                setIsExpanded(true)
                clearQueryParams()
              }}
            />
          </div>
        ) : (
          <div className="flex-1">
            <ChatPanel
              isExpanded={true}
              chatIdFromQuery={chatIdFromQuery}
              onExpandToggle={handleExpandToggle}
              onClose={handleClose}
            />
          </div>
        )}
      </div>
    )
  }

  return (
    <ResizablePanelGroup direction="horizontal">
      <ResizablePanel
        ref={leftPanelRef}
        defaultSize={75}
        minSize={15}
        maxSize={85}
        collapsible={true}
        collapsedSize={0}
        onCollapse={handleLeftPanelCollapse}
        onExpand={handleLeftPanelExpand}
      >
        <Trilhas
          isExpanded={isExpanded}
          setIsExpanded={() => {
            setIsClosed((state) => !state)
            clearQueryParams()
          }}
        />
      </ResizablePanel>

      {!isClosed && (
        <>
          <ResizableHandle className="border-l border-ctx-layout-border" />

          <ResizablePanel defaultSize={30} minSize={30} maxSize={100}>
            <ChatPanel
              isExpanded={isExpanded}
              chatIdFromQuery={chatIdFromQuery}
              onExpandToggle={handleExpandToggle}
              onClose={handleClose}
            />
          </ResizablePanel>
        </>
      )}
    </ResizablePanelGroup>
  )
}

export default function LearningPaths() {
  return (
    <Suspense
      fallback={
        <div className="flex h-screen items-center justify-center">
          <div className="text-muted-foreground">Carregando...</div>
        </div>
      }
    >
      <LearningPathsContent />
    </Suspense>
  )
}
