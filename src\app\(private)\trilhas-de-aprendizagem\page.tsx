'use client'

import { Suspense, useRef, useState } from 'react'
import { ImperativePanelHandle } from 'react-resizable-panels'

import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from '@/components/ui/resizable'
import { useIsMobile } from '@/hooks/use-mobile'
import { useChatStore } from '@/store/useChatStore'

import { ChatPanel } from './chat'
import { Trilhas } from './trilha'

function LearningPathsContent() {
  const isMobile = useIsMobile()

  const { chatId: chatIdFromStore, setChatId } = useChatStore()

  const [isExpanded, setIsExpanded] = useState(false)
  const [isClosed, setIsClosed] = useState(true)

  const leftPanelRef = useRef<ImperativePanelHandle>(null)

  function handleExpandToggle() {
    if (!leftPanelRef.current) return

    if (isExpanded) {
      setIsExpanded(false)

      leftPanelRef.current?.expand()

      leftPanelRef.current?.resize(75)

      return
    }

    setIsExpanded(true)

    setTimeout(() => {
      leftPanelRef.current?.collapse()
    }, 0)
  }

  function handleLeftPanelCollapse() {
    setIsExpanded(true)
  }

  function handleLeftPanelExpand() {
    setIsExpanded(false)
  }

  function clearChatId() {
    setChatId(null)
  }

  function handleClose() {
    setIsClosed(true)
    clearChatId()
  }

  function handleCreateWithAI() {
    setIsClosed(false)
    setIsExpanded(true)
    clearChatId()
  }

  if (isMobile) {
    return (
      <div className="flex h-full flex-col">
        {isClosed ? (
          <div className="flex-1">
            <Trilhas
              isExpanded={isExpanded}
              setIsExpanded={handleCreateWithAI}
            />
          </div>
        ) : (
          <div className="flex-1">
            <ChatPanel
              isExpanded={isExpanded}
              chatIdFromQuery={chatIdFromStore}
              onExpandToggle={handleExpandToggle}
              onClose={handleClose}
            />
          </div>
        )}
      </div>
    )
  }

  return (
    <ResizablePanelGroup direction="horizontal">
      <ResizablePanel
        ref={leftPanelRef}
        defaultSize={isClosed || isExpanded ? 0 : 75}
        minSize={30}
        maxSize={85}
        collapsible={true}
        collapsedSize={0}
        onCollapse={handleLeftPanelCollapse}
        onExpand={handleLeftPanelExpand}
      >
        <Trilhas isExpanded={isExpanded} setIsExpanded={handleCreateWithAI} />
      </ResizablePanel>

      {!isClosed && (
        <>
          <ResizableHandle className="panel-resize-handle border-l border-ctx-layout-border" />

          <ResizablePanel
            defaultSize={100}
            minSize={35}
            maxSize={100}
            className="panel-transition"
          >
            <ChatPanel
              isExpanded={isExpanded}
              onExpandToggle={handleExpandToggle}
              onClose={handleClose}
            />
          </ResizablePanel>
        </>
      )}
    </ResizablePanelGroup>
  )
}

export default function LearningPaths() {
  return (
    <Suspense
      fallback={
        <div className="flex h-screen items-center justify-center">
          <div className="text-muted-foreground">Carregando...</div>
        </div>
      }
    >
      <LearningPathsContent />
    </Suspense>
  )
}
