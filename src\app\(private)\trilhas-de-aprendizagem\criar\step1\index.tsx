'use client'

import { Checkbox, Textarea } from '@ads/components-react'
import { useState } from 'react'
import { Controller, useFormContext } from 'react-hook-form'
import { HiOutlineClipboardDocumentCheck } from 'react-icons/hi2'
import { useInfiniteQuery } from 'react-query'
import Cookies from 'universal-cookie'

import { DatePicker } from '@/components/ui/date-picker'
import { InputField } from '@/components/ui/input-form'
import { Combobox } from '@/components/ui/multiselect-combobox'
import { AsyncCombobox } from '@/components/ui/multiselect-combobox-load'
import { SelectAds } from '@/components/ui/select-ads'
import { EListOrder, EListOrderBy } from '@/graphql/generated/graphql'
import { useDebouncedValue } from '@/hooks/use-debounce'
import { getCollaborators } from '@/http/collaborators/get-collaborators'

import { useStudyPlanQueries } from '../contexts/StudyPlanQueriesContext'
import { CombinedCheckoutType, durationOptions } from '../validations'

export function Step1() {
  const [searchValue, setSearchValue] = useState('')
  const cookies = new Cookies(null, { path: '/' })
  const {
    watch,
    register,
    control,
    formState: { errors },
    resetField,
  } = useFormContext<CombinedCheckoutType>()

  const { useTeamsQuery, useGroupsQuery } = useStudyPlanQueries()

  const searchDebounce = useDebouncedValue(searchValue, 500)

  const {
    data: users,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useInfiniteQuery({
    queryKey: ['collaborators'],
    queryFn: ({ pageParam = 1 }) =>
      getCollaborators({
        page: pageParam,
        limit: 20,
        order: 'ASC' as EListOrder,
        orderBy: 'NAME' as EListOrderBy,
        company_id: Number(cookies.get('b2bCompanyId')),
        enrollment_id: Number(cookies.get('b2bEnrollmentId')),
        q: searchDebounce,
      }),

    getNextPageParam: (response, allPagesData) => {
      const nextPage = allPagesData.length + 1
      return nextPage <= Math.ceil(response.users.total / 10)
        ? nextPage
        : undefined
    },
    keepPreviousData: false,
    staleTime: 60 * 1000 * 10, // 10 minutos
    cacheTime: 0,
  })

  const { data: teams, isLoading: isLoadingTeams } = useTeamsQuery({
    currentPage: 1,
    limit: 10,
  })
  const { data: groups, isLoading: isLoadingGroups } = useGroupsQuery({
    currentPage: 1,
    limit: 10,
  })

  const { useCoursesQuery } = useStudyPlanQueries()

  const { data: b2bCourses, isLoading: isLoadingCourses } = useCoursesQuery({
    currentPage: 1,
    limit: 10,
  })

  const isLoading = isLoadingCourses || isLoadingTeams || isLoadingGroups

  return (
    <div className="space-y-3 rounded-lg bg-ctx-layout-body p-4 lg:p-8">
      <h1 className="flex items-center gap-2 text-ctx-content-title ts-heading-sm">
        <HiOutlineClipboardDocumentCheck />
        Informações Básicas
      </h1>

      <section className="space-y-8 rounded-xl border border-ctx-layout-border p-4">
        <InputField
          id="name"
          label="Nome"
          type="text"
          placeholder="Nome da trilha de aprendizagem"
          className="w-full"
          {...register('name')}
          errorMessage={errors.name?.message}
        />

        <Textarea
          fullWidth
          label="Descrição"
          placeholder="Descrição da trilha de aprendizagem"
          custom={{
            textarea: 'bg-ctx-interactive-secondary',
          }}
          rows={6}
          {...register('description')}
        />

        <div className="grid grid-cols-2 gap-8">
          <Controller
            name="courses"
            control={control}
            render={({ field }) => (
              <Combobox
                label="Cursos"
                placeholder="Selecione um ou mais Cursos para esta trilha"
                options={
                  b2bCourses?.courses.data.map((course) => ({
                    value: String(course.id),
                    label: course.title,
                  })) || []
                }
                onChange={field.onChange}
                value={field.value}
                errorMessage={errors.courses?.message}
                isLoading={isLoadingCourses}
              />
            )}
          />
          <Controller
            name="squads"
            control={control}
            render={({ field }) => (
              <Combobox
                label="Equipes"
                placeholder="Selecione uma ou mais Equipes para esta trilha"
                options={
                  teams?.companySquads.data.map(({ id, title }) => ({
                    value: String(id),
                    label: title,
                  })) || []
                }
                onChange={field.onChange}
                value={field.value}
                errorMessage={errors.squads?.message}
                isLoading={isLoadingTeams}
              />
            )}
          />
          <Controller
            name="groups"
            control={control}
            render={({ field }) => (
              <Combobox
                label="Grupos"
                placeholder="Selecione um ou mais Grupos para esta trilha"
                options={
                  groups?.companyGroups.data.map(({ id, name }) => ({
                    value: String(id),
                    label: name,
                  })) || []
                }
                onChange={field.onChange}
                value={field.value}
                errorMessage={errors.groups?.message}
                isLoading={isLoadingGroups}
              />
            )}
          />
          <Controller
            name="collaborators"
            control={control}
            render={({ field }) => (
              <AsyncCombobox
                label="Colaboradores"
                placeholder="Selecione um ou mais Colaboradores para esta trilha"
                options={
                  users?.pages
                    ?.flatMap((page) => page.users.data)
                    .map(({ id, name }) => ({
                      value: String(id),
                      label: name,
                    })) || []
                }
                onChange={field.onChange}
                handleSearchInput={setSearchValue}
                isFetchingNextPage={isFetchingNextPage}
                isLoading={isLoading}
                hasNextPage={hasNextPage}
                fetchNextPage={fetchNextPage}
              />
            )}
          />
        </div>
        <div className="space-y-4">
          <Controller
            name="isMandatory"
            control={control}
            render={({ field }) => (
              <Checkbox
                className="w-[130px]"
                label="Trilha obrigatória"
                custom={{
                  item: 'bg-ctx-interactive-secondary',
                }}
                checked={field.value}
                onCheckedChange={field.onChange}
              />
            )}
          />
          <div className="flex w-full flex-col gap-4 lg:gap-8 xl:flex-row xl:items-center">
            {watch('isMandatory') && (
              <Controller
                name="duration"
                control={control}
                render={({ field }) => (
                  <div className="h-20 min-w-[208px] lg:w-[280px]">
                    <SelectAds
                      custom={{
                        trigger: 'h-10 bg-ctx-interactive-secondary',
                      }}
                      options={durationOptions}
                      label="Selecionar duração"
                      placeholder="Carga horária"
                      onValueChange={(event) => {
                        field.onChange(event)
                        resetField('startDate')
                        resetField('endDate')
                      }}
                      hasError={!!errors.duration}
                      defaultValue={field.value}
                      value={field.value}
                    />
                    <span className="text-red-500 ts-subtitle-xxxs">
                      {errors.duration?.message}
                    </span>
                  </div>
                )}
              />
            )}

            {watch('duration') === '4' && watch('isMandatory') && (
              <div className="flex w-full flex-col lg:flex-row lg:items-center lg:gap-4">
                <Controller
                  name="startDate"
                  control={control}
                  render={({ field }) => (
                    <DatePicker
                      label="Data de início"
                      placeholder="Selecione a Data de início"
                      value={field.value as Date}
                      onChange={field.onChange}
                      errorMessage={errors.startDate?.message}
                      classNameButton="w-[250px]"
                    />
                  )}
                />

                <span className="mb-2 text-ctx-content-base ts-paragraph-xxs lg:mb-0">
                  até
                </span>

                <Controller
                  name="endDate"
                  control={control}
                  render={({ field }) => (
                    <DatePicker
                      label="Data de término"
                      placeholder="Selecione a Data de término"
                      value={field.value as Date}
                      errorMessage={errors.endDate?.message}
                      onChange={field.onChange}
                      classNameButton="w-[250px]"
                    />
                  )}
                />
              </div>
            )}
          </div>
        </div>
      </section>
    </div>
  )
}
