'use client'

import { usePathname } from 'next/navigation'
import { useEffect } from 'react'
import Cookies from 'universal-cookie'

import { Header } from '@/components/layouts/header/header'
import { AppSidebar } from '@/components/layouts/sidebar'
import { ScrollArea } from '@/components/ui/scroll-area'
import { SidebarProvider } from '@/components/ui/sidebar'
import { clearStates } from '@/utils/clearStates'

type RootLayoutProps = {
  children: React.ReactNode
}

export default function RootLayout({ children }: RootLayoutProps) {
  const cookies = new Cookies(null, { path: '/' })
  const router = usePathname()

  useEffect(() => {
    if (router !== '/trilhas-de-aprendizagem/criar') {
      clearStates()
      cookies.remove('step1')
      cookies.remove('step2')
      cookies.remove('step3')
    }
  }, [router])

  return (
    <SidebarProvider>
      <div className="w-screen">
        <Header />
        <div className="flex min-h-full">
          <AppSidebar />
          <ScrollArea className="max-h-[calc(100vh-4rem)] w-full [&>div>div]:!block">
            <div className="h-full min-h-[calc(100vh-4rem)] bg-ctx-layout-surface md:rounded-tl-[32px]">
              {children}
            </div>
          </ScrollArea>
        </div>
      </div>
    </SidebarProvider>
  )
}
