import {
  Button,
  DropdownItem,
  DropdownItemContent,
  IconShape,
} from '@ads/components-react'
import { Mail } from 'lucide-react'

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'

type SendEmailAlertProps = {
  collaboratorsMutation: () => void
}

export function SendEmailCollaboratorsAlert({
  collaboratorsMutation,
}: SendEmailAlertProps) {
  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <DropdownItem onSelect={(e) => e.preventDefault()}>
          <DropdownItemContent leadingIcon={Mail}>
            E-mail de Boas-vindas
          </DropdownItemContent>
        </DropdownItem>
      </AlertDialogTrigger>
      <AlertDialogContent className="gap-6">
        <IconShape
          size="sm"
          icon={Mail}
          type="brand"
          className="mx-auto bg-ctx-highlight-promote sm:mx-0"
        />
        <AlertDialogHeader>
          <AlertDialogTitle className="text-ctx-content-title ts-heading-md">
            Tem certeza que deseja enviar E-mail de Boas-vindas?
          </AlertDialogTitle>
          <AlertDialogDescription className="text-ctx-content-base ts-paragraph-xs">
            Ao enviar o e-mail, esta ação não poderá ser desfeita.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter className="gap-y-3">
          <AlertDialogCancel asChild>
            <Button size="lg" hierarchy="secondary" className="w-full sm:w-fit">
              Cancelar
            </Button>
          </AlertDialogCancel>
          <AlertDialogAction asChild>
            <Button
              size="lg"
              className="w-full sm:w-fit"
              onClick={collaboratorsMutation}
            >
              Enviar E-mail
            </Button>
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
