import { gql } from 'graphql-request'

export const DELETE_GROUP = gql`
  mutation DeleteCompanyGroup($group_id: Int!) {
    deleteCompanyGroup(group_id: $group_id)
  }
`

export const CREATE_NEW_GROUP = gql`
  mutation CreateCompanyGroup(
    $name: String!
    $select_all_user_ids: Boolean
    $user_ids_selected: [Float!]
    $user_ids_deselected: [Float!]
    $company_id: Int!
  ) {
    createCompanyGroup(
      data: {
        name: $name
        select_all_user_ids: $select_all_user_ids
        user_ids_selected: $user_ids_selected
        user_ids_deselected: $user_ids_deselected
      }
      company_id: $company_id
    ) {
      id
    }
  }
`

export const UPDATE_GROUP = gql`
  mutation UpdateCompanyGroup(
    $id: Float!
    $name: String!
    $select_all_user_ids: Boolean
    $user_ids_selected: [Float!]
    $user_ids_deselected: [Float!]
  ) {
    updateCompanyGroup(
      data: {
        id: $id
        name: $name
        select_all_user_ids: $select_all_user_ids
        user_ids_selected: $user_ids_selected
        user_ids_deselected: $user_ids_deselected
      }
    ) {
      id
    }
  }
`
