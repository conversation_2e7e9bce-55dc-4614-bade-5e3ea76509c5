import { z } from 'zod'

import { globalValidations } from '@/validations/global'

const REQUIRED_FIELD = 'Esse campo é obrigatório'

export const profileSchema = z.object({
  name: globalValidations.text,
  email: globalValidations.email,
  collaboratorType: z
    .string({
      required_error: REQUIRED_FIELD,
    })
    .min(1, {
      message: REQUIRED_FIELD,
    }),
  team: z.string().optional(),
  groups: z
    .array(z.object({ value: z.string(), label: z.string() }))
    .optional(),
  position: z.string().optional(),
  seniority: z.string().optional(),
  birthdate: z
    .string()
    .optional()
    .transform((data) =>
      data ? new Date(data as string).toISOString() : undefined
    ),
  admitedAt: z
    .string()
    .optional()
    .transform((data) =>
      data ? new Date(data as string).toISOString() : undefined
    ),
})

export type ProfileFormData = z.infer<typeof profileSchema>
