import {
  Button,
  DropdownItem,
  DropdownItemContent,
  IconShape,
} from '@ads/components-react'
import { Info, ToggleRight } from 'lucide-react'

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'

type DeleteCollaboratorsAlertProps = {
  collaboratorsMutation: () => void
  currentStatus: boolean
}

export function UpdateCollaboratorsStatusAlert({
  collaboratorsMutation,
  currentStatus,
}: DeleteCollaboratorsAlertProps) {
  const statusToChange = currentStatus ? 'Inativar' : 'Ativar'

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <DropdownItem
          status={currentStatus ? 'destructive' : 'default'}
          onSelect={(e) => e.preventDefault()}
        >
          <DropdownItemContent leadingIcon={ToggleRight}>
            {statusToChange} Colaborador
          </DropdownItemContent>
        </DropdownItem>
      </AlertDialogTrigger>
      <AlertDialogContent className="gap-6">
        <IconShape
          size="sm"
          icon={Info}
          type={currentStatus ? 'danger' : 'brand'}
          className="mx-auto sm:mx-0"
        />
        <AlertDialogHeader>
          <AlertDialogTitle className="text-ctx-content-title ts-heading-sm">
            Tem certeza que deseja {statusToChange.toLowerCase()} este
            colaborador?
          </AlertDialogTitle>

          <AlertDialogDescription className="text-ctx-content-base ts-paragraph-xs">
            {currentStatus
              ? 'Ao inativar o colaborador você não irá encontra-lo em nenhuma busca.'
              : 'Você poderá inativa-lo novamente a qualquer momento.'}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter className="gap-y-3">
          <AlertDialogCancel asChild>
            <Button size="lg" hierarchy="secondary" className="w-full sm:w-fit">
              Cancelar
            </Button>
          </AlertDialogCancel>
          <AlertDialogAction asChild>
            <Button
              size="lg"
              hierarchy={currentStatus ? 'danger' : 'primary'}
              className="w-full sm:w-fit"
              onClick={collaboratorsMutation}
            >
              {statusToChange} Colaborador
            </Button>
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
