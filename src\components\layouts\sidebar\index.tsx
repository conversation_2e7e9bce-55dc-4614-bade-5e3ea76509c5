'use client'

import { <PERSON><PERSON>, <PERSON>conButton } from '@ads/components-react'
import { ArrowLeft, ArrowRight } from 'lucide-react'
import Image from 'next/image'
import * as React from 'react'

import { NavMain } from '@/components/layouts/sidebar/itens'
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  useSidebar,
} from '@/components/ui/sidebar'

import { Separator } from '../../ui/separator'

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { setOpen, open, setOpenMobile, isMobile } = useSidebar()

  const handleCloseSidebar = () => {
    if (isMobile) {
      setOpenMobile(false)
    } else {
      setOpen(false)
    }
  }

  return (
    <Sidebar
      collapsible="icon"
      {...props}
      className="sticky w-[280px] border-none text-ctx-content-base"
    >
      {isMobile && (
        <div className="flex w-full justify-start px-6 py-4">
          <Image
            src={'/assets/solution.webp'}
            alt="Plataforma Solution"
            width={28}
            height={28}
          />
        </div>
      )}
      <SidebarContent>
        <NavMain />
        {/* TODO: Foi comentado que o componente PreviousChats não está sendo utilizado, mas ele é necessário para a funcionalidade de histórico de chats. Se for necessário, descomente a linha abaixo. */}
        {/* {open && (
          <>
            <Separator />
            <PreviousChats />
          </>
        )} */}
      </SidebarContent>
      <SidebarFooter>
        <Separator />
        {open ? (
          <Button
            hierarchy="tertiary"
            leadingIcon={ArrowLeft}
            onClick={handleCloseSidebar}
            className="whitespace-nowrap"
          >
            Ocultar menu lateral
          </Button>
        ) : (
          <IconButton
            icon={ArrowRight}
            hierarchy="tertiary"
            ariaLabel="Expandir menu lateral"
            onClick={() => setOpen(!open)}
          />
        )}
      </SidebarFooter>
    </Sidebar>
  )
}
