/* eslint-disable */
import * as types from './graphql';
import { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';

/**
 * Map of all GraphQL operations in the project.
 *
 * This map has several performance disadvantages:
 * 1. It is not tree-shakeable, so it will include all operations in the project.
 * 2. It is not minifiable, so the string of a GraphQL query will be multiple times inside the bundle.
 * 3. It does not support dead code elimination, so it will add unused operations.
 *
 * Therefore it is highly recommended to use the babel or swc plugin for production.
 * Learn more about it here: https://the-guild.dev/graphql/codegen/plugins/presets/preset-client#reducing-bundle-size
 */
type Documents = {
    "\n  mutation DeleteCollaborator(\n    $user_id: Float!\n    $enrollment_id: Float!\n    $has_to_cancel: Boolean!\n  ) {\n    deleteUserEnrollment(\n      data: {\n        user_id: $user_id\n        enrollment_id: $enrollment_id\n        has_to_cancel: $has_to_cancel\n      }\n    )\n  }\n": typeof types.DeleteCollaboratorDocument,
    "\n  mutation updateRoleUser($user_id: Int!, $role_slugs: [Role!]!) {\n    updateEmployeeRole(user_id: $user_id, role_slugs: $role_slugs) {\n      id\n      roles {\n        slug\n      }\n    }\n  }\n": typeof types.UpdateRoleUserDocument,
    "\n  query GetCollaborators(\n    $page: Float!\n    $limit: Float!\n    $company_id: Float!\n    $enrollment_id: Float!\n    $orderBy: EListOrderBy!\n    $order: EListOrder!\n    $q: String\n  ) {\n    users(\n      page: $page\n      limit: $limit\n      company_id: $company_id\n      orderBy: $orderBy\n      order: $order\n      enrollment_id: $enrollment_id\n      q: $q\n    ) {\n      total\n      perPage\n      data {\n        id\n        name\n        email\n        updated_at\n        seniority\n        position\n        enrollments {\n          id\n        }\n        roles {\n          name\n        }\n        metadata {\n          company_squad {\n            title\n          }\n        }\n      }\n    }\n  }\n": typeof types.GetCollaboratorsDocument,
    "\n  query GetLicensesCount($enrollment_id: Int!) {\n    enrollment(id: $enrollment_id) {\n      b2b_metadata {\n        licenses_count\n        employees_count(employee_status: ACTIVE)\n      }\n    }\n  }\n": typeof types.GetLicensesCountDocument,
    "\n  query getCourses(\n    $page: Float\n    $all: Boolean!\n    $limit: Float\n    $q: String\n    $available_at: AvailabilityFilter\n  ) {\n    courses(\n      all: $all\n      page: $page\n      limit: $limit\n      q: $q\n      available_at: $available_at\n    ) {\n      data {\n        id\n        title\n        categories {\n          title\n        }\n      }\n      total\n      perPage\n    }\n  }\n": typeof types.GetCoursesDocument,
    "\n  query getEnrollmentsByCompanyId($criteria: String!) {\n    company(criteria: $criteria) {\n      enrollments {\n        enrollment_id\n      }\n    }\n  }\n": typeof types.GetEnrollmentsByCompanyIdDocument,
    "\n  mutation DeleteCompanyGroup($group_id: Int!) {\n    deleteCompanyGroup(group_id: $group_id)\n  }\n": typeof types.DeleteCompanyGroupDocument,
    "\n  mutation CreateCompanyGroup(\n    $name: String!\n    $select_all_user_ids: Boolean\n    $user_ids_selected: [Float!]\n    $user_ids_deselected: [Float!]\n    $company_id: Int!\n  ) {\n    createCompanyGroup(\n      data: {\n        name: $name\n        select_all_user_ids: $select_all_user_ids\n        user_ids_selected: $user_ids_selected\n        user_ids_deselected: $user_ids_deselected\n      }\n      company_id: $company_id\n    ) {\n      id\n    }\n  }\n": typeof types.CreateCompanyGroupDocument,
    "\n  mutation UpdateCompanyGroup(\n    $id: Float!\n    $name: String!\n    $select_all_user_ids: Boolean\n    $user_ids_selected: [Float!]\n    $user_ids_deselected: [Float!]\n  ) {\n    updateCompanyGroup(\n      data: {\n        id: $id\n        name: $name\n        select_all_user_ids: $select_all_user_ids\n        user_ids_selected: $user_ids_selected\n        user_ids_deselected: $user_ids_deselected\n      }\n    ) {\n      id\n    }\n  }\n": typeof types.UpdateCompanyGroupDocument,
    "\n  query GetB2BUsers(\n    $all: Boolean!\n    $page: Float!\n    $limit: Float!\n    $orderBy: EListOrderBy!\n    $order: EListOrder!\n    $company_id: Float!\n    $enrollment_id: Float!\n    $q: String\n    $hasSquad: Boolean\n  ) {\n    users(\n      all: $all\n      page: $page\n      limit: $limit\n      orderBy: $orderBy\n      order: $order\n      company_id: $company_id\n      enrollment_id: $enrollment_id\n      q: $q\n      hasSquad: $hasSquad\n    ) {\n      total\n      perPage\n      data {\n        id\n        name\n        email\n        last_login\n        enrollments_pivot {\n          status\n        }\n        roles {\n          id\n          name\n          slug\n        }\n        metadata {\n          last_activity_completed_at\n          activities_completed\n          squad_id\n          company_squad {\n            id\n            title\n          }\n        }\n      }\n    }\n  }\n": typeof types.GetB2BUsersDocument,
    "\n  query GetGroups(\n    $limit: Float\n    $page: Float\n    $company_id: Float!\n    $name: String\n  ) {\n    companyGroups(\n      limit: $limit\n      page: $page\n      company_id: $company_id\n      name: $name\n    ) {\n      data {\n        id\n        name\n        users_count\n        updated_at\n        company_id\n        company_group_users_pivot {\n          group_id\n          user_id\n        }\n      }\n      total\n      perPage\n    }\n  }\n": typeof types.GetGroupsDocument,
    "\n  query GetGroup($id: Int!, $page: Int, $limit: Int) {\n    companyGroup(id: $id) {\n      id\n      name\n      groupUsers(limit: $limit, page: $page) {\n        total\n        perPage\n        data {\n          id\n          name\n          email\n          updated_at\n        }\n      }\n    }\n  }\n": typeof types.GetGroupDocument,
    "\n  query getLicenseActivesLMS {\n    userEnrollments {\n      id\n      type\n    }\n  }\n": typeof types.GetLicenseActivesLmsDocument,
    "\n  query getUserProfile {\n    profile {\n      payment_plan {\n        slug\n      }\n    }\n  }\n": typeof types.GetUserProfileDocument,
    "\n  mutation CreateStudyPlan(\n    $name: String!\n    $description: String!\n    $courses: [StudyPlanCoursePivot!]!\n    $company_id: Float!\n    $squad_ids: [Float!]\n    $user_ids: [Float!]\n    $is_pdi: Boolean\n    $is_for_all_users: Boolean\n    $is_for_all_squads: Boolean\n    $is_for_all_courses: Boolean\n  ) {\n    createStudyPlan(\n      data: {\n        name: $name\n        description: $description\n        courses: $courses\n        company_id: $company_id\n        squad_ids: $squad_ids\n        user_ids: $user_ids\n        is_pdi: $is_pdi\n        is_for_all_users: $is_for_all_users\n        is_for_all_squads: $is_for_all_squads\n        is_for_all_courses: $is_for_all_courses\n      }\n    ) {\n      id\n    }\n  }\n": typeof types.CreateStudyPlanDocument,
    "\n  mutation deleteStudyPlan($id: Float!) {\n    deleteStudyPlan(id: $id)\n  }\n": typeof types.DeleteStudyPlanDocument,
    "\n  mutation UpdatedTeamsAndUserPlanStudy(\n    $id: Float!\n    $squad_ids: [Float!]!\n    $user_ids: [Float!]!\n    $notify: Boolean!\n    $is_for_all_users: Boolean\n    $is_for_all_squads: Boolean\n    $is_for_all_courses: Boolean\n  ) {\n    updateStudyPlan(\n      data: {\n        id: $id\n        squad_ids: $squad_ids\n        user_ids: $user_ids\n        notify: $notify\n        is_for_all_users: $is_for_all_users\n        is_for_all_squads: $is_for_all_squads\n        is_for_all_courses: $is_for_all_courses\n      }\n    ) {\n      id\n    }\n  }\n": typeof types.UpdatedTeamsAndUserPlanStudyDocument,
    "\n  query getStudyPlans(\n    $user_id: Float\n    $name: String\n    $company_id: Float!\n    $limit: Float\n    $page: Float\n    $onlyPDI: Boolean\n    $status: Boolean\n    $end_date: DateTime\n  ) {\n    studyPlans(\n      name: $name\n      company_id: $company_id\n      limit: $limit\n      page: $page\n      onlyPDI: $onlyPDI\n      user_id: $user_id\n      status: $status\n      end_date: $end_date\n    ) {\n      data {\n        courses_pivot {\n          course_id\n          course {\n            id\n            title\n          }\n        }\n        ai_generated\n        end_date\n        status\n        id\n        name\n        coursesCount\n        squadsCount\n        usersCount\n        users_completed_count\n      }\n      total\n      perPage\n    }\n  }\n": typeof types.GetStudyPlansDocument,
    "\n  mutation updateCompanySquad(\n    $id: Float!\n    $title: String!\n    $company_id: Float!\n    $user_ids: [Float!]\n  ) {\n    updateCompanySquad(\n      data: {\n        id: $id\n        title: $title\n        company_id: $company_id\n        user_ids: $user_ids\n      }\n    ) {\n      id\n    }\n  }\n": typeof types.UpdateCompanySquadDocument,
    "\n  mutation deleteCompanySquad($id: Int!) {\n    deleteCompanySquad(id: $id)\n  }\n": typeof types.DeleteCompanySquadDocument,
    "\n  query getTeamById($id: Int!, $page: Int, $limit: Int) {\n    companySquad(id: $id) {\n      id\n      title\n      users(limit: $limit, page: $page) {\n        total\n        perPage\n        data {\n          id\n          name\n          email\n          updated_at\n        }\n      }\n    }\n  }\n": typeof types.GetTeamByIdDocument,
    "\n  query getTeams(\n    $limit: Float\n    $page: Float\n    $company_id: Float\n    $title: String\n  ) {\n    companySquads(\n      limit: $limit\n      page: $page\n      company_id: $company_id\n      title: $title\n    ) {\n      data {\n        id\n        title\n        users_count\n        updated_at\n      }\n      total\n      perPage\n    }\n  }\n": typeof types.GetTeamsDocument,
    "\n  mutation CreateCompanySquad(\n    $title: String!\n    $company_id: Float!\n    $user_ids: [Float!]\n  ) {\n    createCompanySquad(\n      data: { title: $title, company_id: $company_id, user_ids: $user_ids }\n    ) {\n      id\n    }\n  }\n": typeof types.CreateCompanySquadDocument,
};
const documents: Documents = {
    "\n  mutation DeleteCollaborator(\n    $user_id: Float!\n    $enrollment_id: Float!\n    $has_to_cancel: Boolean!\n  ) {\n    deleteUserEnrollment(\n      data: {\n        user_id: $user_id\n        enrollment_id: $enrollment_id\n        has_to_cancel: $has_to_cancel\n      }\n    )\n  }\n": types.DeleteCollaboratorDocument,
    "\n  mutation updateRoleUser($user_id: Int!, $role_slugs: [Role!]!) {\n    updateEmployeeRole(user_id: $user_id, role_slugs: $role_slugs) {\n      id\n      roles {\n        slug\n      }\n    }\n  }\n": types.UpdateRoleUserDocument,
    "\n  query GetCollaborators(\n    $page: Float!\n    $limit: Float!\n    $company_id: Float!\n    $enrollment_id: Float!\n    $orderBy: EListOrderBy!\n    $order: EListOrder!\n    $q: String\n  ) {\n    users(\n      page: $page\n      limit: $limit\n      company_id: $company_id\n      orderBy: $orderBy\n      order: $order\n      enrollment_id: $enrollment_id\n      q: $q\n    ) {\n      total\n      perPage\n      data {\n        id\n        name\n        email\n        updated_at\n        seniority\n        position\n        enrollments {\n          id\n        }\n        roles {\n          name\n        }\n        metadata {\n          company_squad {\n            title\n          }\n        }\n      }\n    }\n  }\n": types.GetCollaboratorsDocument,
    "\n  query GetLicensesCount($enrollment_id: Int!) {\n    enrollment(id: $enrollment_id) {\n      b2b_metadata {\n        licenses_count\n        employees_count(employee_status: ACTIVE)\n      }\n    }\n  }\n": types.GetLicensesCountDocument,
    "\n  query getCourses(\n    $page: Float\n    $all: Boolean!\n    $limit: Float\n    $q: String\n    $available_at: AvailabilityFilter\n  ) {\n    courses(\n      all: $all\n      page: $page\n      limit: $limit\n      q: $q\n      available_at: $available_at\n    ) {\n      data {\n        id\n        title\n        categories {\n          title\n        }\n      }\n      total\n      perPage\n    }\n  }\n": types.GetCoursesDocument,
    "\n  query getEnrollmentsByCompanyId($criteria: String!) {\n    company(criteria: $criteria) {\n      enrollments {\n        enrollment_id\n      }\n    }\n  }\n": types.GetEnrollmentsByCompanyIdDocument,
    "\n  mutation DeleteCompanyGroup($group_id: Int!) {\n    deleteCompanyGroup(group_id: $group_id)\n  }\n": types.DeleteCompanyGroupDocument,
    "\n  mutation CreateCompanyGroup(\n    $name: String!\n    $select_all_user_ids: Boolean\n    $user_ids_selected: [Float!]\n    $user_ids_deselected: [Float!]\n    $company_id: Int!\n  ) {\n    createCompanyGroup(\n      data: {\n        name: $name\n        select_all_user_ids: $select_all_user_ids\n        user_ids_selected: $user_ids_selected\n        user_ids_deselected: $user_ids_deselected\n      }\n      company_id: $company_id\n    ) {\n      id\n    }\n  }\n": types.CreateCompanyGroupDocument,
    "\n  mutation UpdateCompanyGroup(\n    $id: Float!\n    $name: String!\n    $select_all_user_ids: Boolean\n    $user_ids_selected: [Float!]\n    $user_ids_deselected: [Float!]\n  ) {\n    updateCompanyGroup(\n      data: {\n        id: $id\n        name: $name\n        select_all_user_ids: $select_all_user_ids\n        user_ids_selected: $user_ids_selected\n        user_ids_deselected: $user_ids_deselected\n      }\n    ) {\n      id\n    }\n  }\n": types.UpdateCompanyGroupDocument,
    "\n  query GetB2BUsers(\n    $all: Boolean!\n    $page: Float!\n    $limit: Float!\n    $orderBy: EListOrderBy!\n    $order: EListOrder!\n    $company_id: Float!\n    $enrollment_id: Float!\n    $q: String\n    $hasSquad: Boolean\n  ) {\n    users(\n      all: $all\n      page: $page\n      limit: $limit\n      orderBy: $orderBy\n      order: $order\n      company_id: $company_id\n      enrollment_id: $enrollment_id\n      q: $q\n      hasSquad: $hasSquad\n    ) {\n      total\n      perPage\n      data {\n        id\n        name\n        email\n        last_login\n        enrollments_pivot {\n          status\n        }\n        roles {\n          id\n          name\n          slug\n        }\n        metadata {\n          last_activity_completed_at\n          activities_completed\n          squad_id\n          company_squad {\n            id\n            title\n          }\n        }\n      }\n    }\n  }\n": types.GetB2BUsersDocument,
    "\n  query GetGroups(\n    $limit: Float\n    $page: Float\n    $company_id: Float!\n    $name: String\n  ) {\n    companyGroups(\n      limit: $limit\n      page: $page\n      company_id: $company_id\n      name: $name\n    ) {\n      data {\n        id\n        name\n        users_count\n        updated_at\n        company_id\n        company_group_users_pivot {\n          group_id\n          user_id\n        }\n      }\n      total\n      perPage\n    }\n  }\n": types.GetGroupsDocument,
    "\n  query GetGroup($id: Int!, $page: Int, $limit: Int) {\n    companyGroup(id: $id) {\n      id\n      name\n      groupUsers(limit: $limit, page: $page) {\n        total\n        perPage\n        data {\n          id\n          name\n          email\n          updated_at\n        }\n      }\n    }\n  }\n": types.GetGroupDocument,
    "\n  query getLicenseActivesLMS {\n    userEnrollments {\n      id\n      type\n    }\n  }\n": types.GetLicenseActivesLmsDocument,
    "\n  query getUserProfile {\n    profile {\n      payment_plan {\n        slug\n      }\n    }\n  }\n": types.GetUserProfileDocument,
    "\n  mutation CreateStudyPlan(\n    $name: String!\n    $description: String!\n    $courses: [StudyPlanCoursePivot!]!\n    $company_id: Float!\n    $squad_ids: [Float!]\n    $user_ids: [Float!]\n    $is_pdi: Boolean\n    $is_for_all_users: Boolean\n    $is_for_all_squads: Boolean\n    $is_for_all_courses: Boolean\n  ) {\n    createStudyPlan(\n      data: {\n        name: $name\n        description: $description\n        courses: $courses\n        company_id: $company_id\n        squad_ids: $squad_ids\n        user_ids: $user_ids\n        is_pdi: $is_pdi\n        is_for_all_users: $is_for_all_users\n        is_for_all_squads: $is_for_all_squads\n        is_for_all_courses: $is_for_all_courses\n      }\n    ) {\n      id\n    }\n  }\n": types.CreateStudyPlanDocument,
    "\n  mutation deleteStudyPlan($id: Float!) {\n    deleteStudyPlan(id: $id)\n  }\n": types.DeleteStudyPlanDocument,
    "\n  mutation UpdatedTeamsAndUserPlanStudy(\n    $id: Float!\n    $squad_ids: [Float!]!\n    $user_ids: [Float!]!\n    $notify: Boolean!\n    $is_for_all_users: Boolean\n    $is_for_all_squads: Boolean\n    $is_for_all_courses: Boolean\n  ) {\n    updateStudyPlan(\n      data: {\n        id: $id\n        squad_ids: $squad_ids\n        user_ids: $user_ids\n        notify: $notify\n        is_for_all_users: $is_for_all_users\n        is_for_all_squads: $is_for_all_squads\n        is_for_all_courses: $is_for_all_courses\n      }\n    ) {\n      id\n    }\n  }\n": types.UpdatedTeamsAndUserPlanStudyDocument,
    "\n  query getStudyPlans(\n    $user_id: Float\n    $name: String\n    $company_id: Float!\n    $limit: Float\n    $page: Float\n    $onlyPDI: Boolean\n    $status: Boolean\n    $end_date: DateTime\n  ) {\n    studyPlans(\n      name: $name\n      company_id: $company_id\n      limit: $limit\n      page: $page\n      onlyPDI: $onlyPDI\n      user_id: $user_id\n      status: $status\n      end_date: $end_date\n    ) {\n      data {\n        courses_pivot {\n          course_id\n          course {\n            id\n            title\n          }\n        }\n        ai_generated\n        end_date\n        status\n        id\n        name\n        coursesCount\n        squadsCount\n        usersCount\n        users_completed_count\n      }\n      total\n      perPage\n    }\n  }\n": types.GetStudyPlansDocument,
    "\n  mutation updateCompanySquad(\n    $id: Float!\n    $title: String!\n    $company_id: Float!\n    $user_ids: [Float!]\n  ) {\n    updateCompanySquad(\n      data: {\n        id: $id\n        title: $title\n        company_id: $company_id\n        user_ids: $user_ids\n      }\n    ) {\n      id\n    }\n  }\n": types.UpdateCompanySquadDocument,
    "\n  mutation deleteCompanySquad($id: Int!) {\n    deleteCompanySquad(id: $id)\n  }\n": types.DeleteCompanySquadDocument,
    "\n  query getTeamById($id: Int!, $page: Int, $limit: Int) {\n    companySquad(id: $id) {\n      id\n      title\n      users(limit: $limit, page: $page) {\n        total\n        perPage\n        data {\n          id\n          name\n          email\n          updated_at\n        }\n      }\n    }\n  }\n": types.GetTeamByIdDocument,
    "\n  query getTeams(\n    $limit: Float\n    $page: Float\n    $company_id: Float\n    $title: String\n  ) {\n    companySquads(\n      limit: $limit\n      page: $page\n      company_id: $company_id\n      title: $title\n    ) {\n      data {\n        id\n        title\n        users_count\n        updated_at\n      }\n      total\n      perPage\n    }\n  }\n": types.GetTeamsDocument,
    "\n  mutation CreateCompanySquad(\n    $title: String!\n    $company_id: Float!\n    $user_ids: [Float!]\n  ) {\n    createCompanySquad(\n      data: { title: $title, company_id: $company_id, user_ids: $user_ids }\n    ) {\n      id\n    }\n  }\n": types.CreateCompanySquadDocument,
};

/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 *
 *
 * @example
 * ```ts
 * const query = graphql(`query GetUser($id: ID!) { user(id: $id) { name } }`);
 * ```
 *
 * The query argument is unknown!
 * Please regenerate the types.
 */
export function graphql(source: string): unknown;

/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation DeleteCollaborator(\n    $user_id: Float!\n    $enrollment_id: Float!\n    $has_to_cancel: Boolean!\n  ) {\n    deleteUserEnrollment(\n      data: {\n        user_id: $user_id\n        enrollment_id: $enrollment_id\n        has_to_cancel: $has_to_cancel\n      }\n    )\n  }\n"): (typeof documents)["\n  mutation DeleteCollaborator(\n    $user_id: Float!\n    $enrollment_id: Float!\n    $has_to_cancel: Boolean!\n  ) {\n    deleteUserEnrollment(\n      data: {\n        user_id: $user_id\n        enrollment_id: $enrollment_id\n        has_to_cancel: $has_to_cancel\n      }\n    )\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation updateRoleUser($user_id: Int!, $role_slugs: [Role!]!) {\n    updateEmployeeRole(user_id: $user_id, role_slugs: $role_slugs) {\n      id\n      roles {\n        slug\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation updateRoleUser($user_id: Int!, $role_slugs: [Role!]!) {\n    updateEmployeeRole(user_id: $user_id, role_slugs: $role_slugs) {\n      id\n      roles {\n        slug\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetCollaborators(\n    $page: Float!\n    $limit: Float!\n    $company_id: Float!\n    $enrollment_id: Float!\n    $orderBy: EListOrderBy!\n    $order: EListOrder!\n    $q: String\n  ) {\n    users(\n      page: $page\n      limit: $limit\n      company_id: $company_id\n      orderBy: $orderBy\n      order: $order\n      enrollment_id: $enrollment_id\n      q: $q\n    ) {\n      total\n      perPage\n      data {\n        id\n        name\n        email\n        updated_at\n        seniority\n        position\n        enrollments {\n          id\n        }\n        roles {\n          name\n        }\n        metadata {\n          company_squad {\n            title\n          }\n        }\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetCollaborators(\n    $page: Float!\n    $limit: Float!\n    $company_id: Float!\n    $enrollment_id: Float!\n    $orderBy: EListOrderBy!\n    $order: EListOrder!\n    $q: String\n  ) {\n    users(\n      page: $page\n      limit: $limit\n      company_id: $company_id\n      orderBy: $orderBy\n      order: $order\n      enrollment_id: $enrollment_id\n      q: $q\n    ) {\n      total\n      perPage\n      data {\n        id\n        name\n        email\n        updated_at\n        seniority\n        position\n        enrollments {\n          id\n        }\n        roles {\n          name\n        }\n        metadata {\n          company_squad {\n            title\n          }\n        }\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetLicensesCount($enrollment_id: Int!) {\n    enrollment(id: $enrollment_id) {\n      b2b_metadata {\n        licenses_count\n        employees_count(employee_status: ACTIVE)\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetLicensesCount($enrollment_id: Int!) {\n    enrollment(id: $enrollment_id) {\n      b2b_metadata {\n        licenses_count\n        employees_count(employee_status: ACTIVE)\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getCourses(\n    $page: Float\n    $all: Boolean!\n    $limit: Float\n    $q: String\n    $available_at: AvailabilityFilter\n  ) {\n    courses(\n      all: $all\n      page: $page\n      limit: $limit\n      q: $q\n      available_at: $available_at\n    ) {\n      data {\n        id\n        title\n        categories {\n          title\n        }\n      }\n      total\n      perPage\n    }\n  }\n"): (typeof documents)["\n  query getCourses(\n    $page: Float\n    $all: Boolean!\n    $limit: Float\n    $q: String\n    $available_at: AvailabilityFilter\n  ) {\n    courses(\n      all: $all\n      page: $page\n      limit: $limit\n      q: $q\n      available_at: $available_at\n    ) {\n      data {\n        id\n        title\n        categories {\n          title\n        }\n      }\n      total\n      perPage\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getEnrollmentsByCompanyId($criteria: String!) {\n    company(criteria: $criteria) {\n      enrollments {\n        enrollment_id\n      }\n    }\n  }\n"): (typeof documents)["\n  query getEnrollmentsByCompanyId($criteria: String!) {\n    company(criteria: $criteria) {\n      enrollments {\n        enrollment_id\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation DeleteCompanyGroup($group_id: Int!) {\n    deleteCompanyGroup(group_id: $group_id)\n  }\n"): (typeof documents)["\n  mutation DeleteCompanyGroup($group_id: Int!) {\n    deleteCompanyGroup(group_id: $group_id)\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation CreateCompanyGroup(\n    $name: String!\n    $select_all_user_ids: Boolean\n    $user_ids_selected: [Float!]\n    $user_ids_deselected: [Float!]\n    $company_id: Int!\n  ) {\n    createCompanyGroup(\n      data: {\n        name: $name\n        select_all_user_ids: $select_all_user_ids\n        user_ids_selected: $user_ids_selected\n        user_ids_deselected: $user_ids_deselected\n      }\n      company_id: $company_id\n    ) {\n      id\n    }\n  }\n"): (typeof documents)["\n  mutation CreateCompanyGroup(\n    $name: String!\n    $select_all_user_ids: Boolean\n    $user_ids_selected: [Float!]\n    $user_ids_deselected: [Float!]\n    $company_id: Int!\n  ) {\n    createCompanyGroup(\n      data: {\n        name: $name\n        select_all_user_ids: $select_all_user_ids\n        user_ids_selected: $user_ids_selected\n        user_ids_deselected: $user_ids_deselected\n      }\n      company_id: $company_id\n    ) {\n      id\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation UpdateCompanyGroup(\n    $id: Float!\n    $name: String!\n    $select_all_user_ids: Boolean\n    $user_ids_selected: [Float!]\n    $user_ids_deselected: [Float!]\n  ) {\n    updateCompanyGroup(\n      data: {\n        id: $id\n        name: $name\n        select_all_user_ids: $select_all_user_ids\n        user_ids_selected: $user_ids_selected\n        user_ids_deselected: $user_ids_deselected\n      }\n    ) {\n      id\n    }\n  }\n"): (typeof documents)["\n  mutation UpdateCompanyGroup(\n    $id: Float!\n    $name: String!\n    $select_all_user_ids: Boolean\n    $user_ids_selected: [Float!]\n    $user_ids_deselected: [Float!]\n  ) {\n    updateCompanyGroup(\n      data: {\n        id: $id\n        name: $name\n        select_all_user_ids: $select_all_user_ids\n        user_ids_selected: $user_ids_selected\n        user_ids_deselected: $user_ids_deselected\n      }\n    ) {\n      id\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetB2BUsers(\n    $all: Boolean!\n    $page: Float!\n    $limit: Float!\n    $orderBy: EListOrderBy!\n    $order: EListOrder!\n    $company_id: Float!\n    $enrollment_id: Float!\n    $q: String\n    $hasSquad: Boolean\n  ) {\n    users(\n      all: $all\n      page: $page\n      limit: $limit\n      orderBy: $orderBy\n      order: $order\n      company_id: $company_id\n      enrollment_id: $enrollment_id\n      q: $q\n      hasSquad: $hasSquad\n    ) {\n      total\n      perPage\n      data {\n        id\n        name\n        email\n        last_login\n        enrollments_pivot {\n          status\n        }\n        roles {\n          id\n          name\n          slug\n        }\n        metadata {\n          last_activity_completed_at\n          activities_completed\n          squad_id\n          company_squad {\n            id\n            title\n          }\n        }\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetB2BUsers(\n    $all: Boolean!\n    $page: Float!\n    $limit: Float!\n    $orderBy: EListOrderBy!\n    $order: EListOrder!\n    $company_id: Float!\n    $enrollment_id: Float!\n    $q: String\n    $hasSquad: Boolean\n  ) {\n    users(\n      all: $all\n      page: $page\n      limit: $limit\n      orderBy: $orderBy\n      order: $order\n      company_id: $company_id\n      enrollment_id: $enrollment_id\n      q: $q\n      hasSquad: $hasSquad\n    ) {\n      total\n      perPage\n      data {\n        id\n        name\n        email\n        last_login\n        enrollments_pivot {\n          status\n        }\n        roles {\n          id\n          name\n          slug\n        }\n        metadata {\n          last_activity_completed_at\n          activities_completed\n          squad_id\n          company_squad {\n            id\n            title\n          }\n        }\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetGroups(\n    $limit: Float\n    $page: Float\n    $company_id: Float!\n    $name: String\n  ) {\n    companyGroups(\n      limit: $limit\n      page: $page\n      company_id: $company_id\n      name: $name\n    ) {\n      data {\n        id\n        name\n        users_count\n        updated_at\n        company_id\n        company_group_users_pivot {\n          group_id\n          user_id\n        }\n      }\n      total\n      perPage\n    }\n  }\n"): (typeof documents)["\n  query GetGroups(\n    $limit: Float\n    $page: Float\n    $company_id: Float!\n    $name: String\n  ) {\n    companyGroups(\n      limit: $limit\n      page: $page\n      company_id: $company_id\n      name: $name\n    ) {\n      data {\n        id\n        name\n        users_count\n        updated_at\n        company_id\n        company_group_users_pivot {\n          group_id\n          user_id\n        }\n      }\n      total\n      perPage\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetGroup($id: Int!, $page: Int, $limit: Int) {\n    companyGroup(id: $id) {\n      id\n      name\n      groupUsers(limit: $limit, page: $page) {\n        total\n        perPage\n        data {\n          id\n          name\n          email\n          updated_at\n        }\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetGroup($id: Int!, $page: Int, $limit: Int) {\n    companyGroup(id: $id) {\n      id\n      name\n      groupUsers(limit: $limit, page: $page) {\n        total\n        perPage\n        data {\n          id\n          name\n          email\n          updated_at\n        }\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getLicenseActivesLMS {\n    userEnrollments {\n      id\n      type\n    }\n  }\n"): (typeof documents)["\n  query getLicenseActivesLMS {\n    userEnrollments {\n      id\n      type\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getUserProfile {\n    profile {\n      payment_plan {\n        slug\n      }\n    }\n  }\n"): (typeof documents)["\n  query getUserProfile {\n    profile {\n      payment_plan {\n        slug\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation CreateStudyPlan(\n    $name: String!\n    $description: String!\n    $courses: [StudyPlanCoursePivot!]!\n    $company_id: Float!\n    $squad_ids: [Float!]\n    $user_ids: [Float!]\n    $is_pdi: Boolean\n    $is_for_all_users: Boolean\n    $is_for_all_squads: Boolean\n    $is_for_all_courses: Boolean\n  ) {\n    createStudyPlan(\n      data: {\n        name: $name\n        description: $description\n        courses: $courses\n        company_id: $company_id\n        squad_ids: $squad_ids\n        user_ids: $user_ids\n        is_pdi: $is_pdi\n        is_for_all_users: $is_for_all_users\n        is_for_all_squads: $is_for_all_squads\n        is_for_all_courses: $is_for_all_courses\n      }\n    ) {\n      id\n    }\n  }\n"): (typeof documents)["\n  mutation CreateStudyPlan(\n    $name: String!\n    $description: String!\n    $courses: [StudyPlanCoursePivot!]!\n    $company_id: Float!\n    $squad_ids: [Float!]\n    $user_ids: [Float!]\n    $is_pdi: Boolean\n    $is_for_all_users: Boolean\n    $is_for_all_squads: Boolean\n    $is_for_all_courses: Boolean\n  ) {\n    createStudyPlan(\n      data: {\n        name: $name\n        description: $description\n        courses: $courses\n        company_id: $company_id\n        squad_ids: $squad_ids\n        user_ids: $user_ids\n        is_pdi: $is_pdi\n        is_for_all_users: $is_for_all_users\n        is_for_all_squads: $is_for_all_squads\n        is_for_all_courses: $is_for_all_courses\n      }\n    ) {\n      id\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation deleteStudyPlan($id: Float!) {\n    deleteStudyPlan(id: $id)\n  }\n"): (typeof documents)["\n  mutation deleteStudyPlan($id: Float!) {\n    deleteStudyPlan(id: $id)\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation UpdatedTeamsAndUserPlanStudy(\n    $id: Float!\n    $squad_ids: [Float!]!\n    $user_ids: [Float!]!\n    $notify: Boolean!\n    $is_for_all_users: Boolean\n    $is_for_all_squads: Boolean\n    $is_for_all_courses: Boolean\n  ) {\n    updateStudyPlan(\n      data: {\n        id: $id\n        squad_ids: $squad_ids\n        user_ids: $user_ids\n        notify: $notify\n        is_for_all_users: $is_for_all_users\n        is_for_all_squads: $is_for_all_squads\n        is_for_all_courses: $is_for_all_courses\n      }\n    ) {\n      id\n    }\n  }\n"): (typeof documents)["\n  mutation UpdatedTeamsAndUserPlanStudy(\n    $id: Float!\n    $squad_ids: [Float!]!\n    $user_ids: [Float!]!\n    $notify: Boolean!\n    $is_for_all_users: Boolean\n    $is_for_all_squads: Boolean\n    $is_for_all_courses: Boolean\n  ) {\n    updateStudyPlan(\n      data: {\n        id: $id\n        squad_ids: $squad_ids\n        user_ids: $user_ids\n        notify: $notify\n        is_for_all_users: $is_for_all_users\n        is_for_all_squads: $is_for_all_squads\n        is_for_all_courses: $is_for_all_courses\n      }\n    ) {\n      id\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getStudyPlans(\n    $user_id: Float\n    $name: String\n    $company_id: Float!\n    $limit: Float\n    $page: Float\n    $onlyPDI: Boolean\n    $status: Boolean\n    $end_date: DateTime\n  ) {\n    studyPlans(\n      name: $name\n      company_id: $company_id\n      limit: $limit\n      page: $page\n      onlyPDI: $onlyPDI\n      user_id: $user_id\n      status: $status\n      end_date: $end_date\n    ) {\n      data {\n        courses_pivot {\n          course_id\n          course {\n            id\n            title\n          }\n        }\n        ai_generated\n        end_date\n        status\n        id\n        name\n        coursesCount\n        squadsCount\n        usersCount\n        users_completed_count\n      }\n      total\n      perPage\n    }\n  }\n"): (typeof documents)["\n  query getStudyPlans(\n    $user_id: Float\n    $name: String\n    $company_id: Float!\n    $limit: Float\n    $page: Float\n    $onlyPDI: Boolean\n    $status: Boolean\n    $end_date: DateTime\n  ) {\n    studyPlans(\n      name: $name\n      company_id: $company_id\n      limit: $limit\n      page: $page\n      onlyPDI: $onlyPDI\n      user_id: $user_id\n      status: $status\n      end_date: $end_date\n    ) {\n      data {\n        courses_pivot {\n          course_id\n          course {\n            id\n            title\n          }\n        }\n        ai_generated\n        end_date\n        status\n        id\n        name\n        coursesCount\n        squadsCount\n        usersCount\n        users_completed_count\n      }\n      total\n      perPage\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation updateCompanySquad(\n    $id: Float!\n    $title: String!\n    $company_id: Float!\n    $user_ids: [Float!]\n  ) {\n    updateCompanySquad(\n      data: {\n        id: $id\n        title: $title\n        company_id: $company_id\n        user_ids: $user_ids\n      }\n    ) {\n      id\n    }\n  }\n"): (typeof documents)["\n  mutation updateCompanySquad(\n    $id: Float!\n    $title: String!\n    $company_id: Float!\n    $user_ids: [Float!]\n  ) {\n    updateCompanySquad(\n      data: {\n        id: $id\n        title: $title\n        company_id: $company_id\n        user_ids: $user_ids\n      }\n    ) {\n      id\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation deleteCompanySquad($id: Int!) {\n    deleteCompanySquad(id: $id)\n  }\n"): (typeof documents)["\n  mutation deleteCompanySquad($id: Int!) {\n    deleteCompanySquad(id: $id)\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getTeamById($id: Int!, $page: Int, $limit: Int) {\n    companySquad(id: $id) {\n      id\n      title\n      users(limit: $limit, page: $page) {\n        total\n        perPage\n        data {\n          id\n          name\n          email\n          updated_at\n        }\n      }\n    }\n  }\n"): (typeof documents)["\n  query getTeamById($id: Int!, $page: Int, $limit: Int) {\n    companySquad(id: $id) {\n      id\n      title\n      users(limit: $limit, page: $page) {\n        total\n        perPage\n        data {\n          id\n          name\n          email\n          updated_at\n        }\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getTeams(\n    $limit: Float\n    $page: Float\n    $company_id: Float\n    $title: String\n  ) {\n    companySquads(\n      limit: $limit\n      page: $page\n      company_id: $company_id\n      title: $title\n    ) {\n      data {\n        id\n        title\n        users_count\n        updated_at\n      }\n      total\n      perPage\n    }\n  }\n"): (typeof documents)["\n  query getTeams(\n    $limit: Float\n    $page: Float\n    $company_id: Float\n    $title: String\n  ) {\n    companySquads(\n      limit: $limit\n      page: $page\n      company_id: $company_id\n      title: $title\n    ) {\n      data {\n        id\n        title\n        users_count\n        updated_at\n      }\n      total\n      perPage\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation CreateCompanySquad(\n    $title: String!\n    $company_id: Float!\n    $user_ids: [Float!]\n  ) {\n    createCompanySquad(\n      data: { title: $title, company_id: $company_id, user_ids: $user_ids }\n    ) {\n      id\n    }\n  }\n"): (typeof documents)["\n  mutation CreateCompanySquad(\n    $title: String!\n    $company_id: Float!\n    $user_ids: [Float!]\n  ) {\n    createCompanySquad(\n      data: { title: $title, company_id: $company_id, user_ids: $user_ids }\n    ) {\n      id\n    }\n  }\n"];

export function graphql(source: string) {
  return (documents as any)[source] ?? {};
}

export type DocumentType<TDocumentNode extends DocumentNode<any, any>> = TDocumentNode extends DocumentNode<  infer TType,  any>  ? TType  : never;